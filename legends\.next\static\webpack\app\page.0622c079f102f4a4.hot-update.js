"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/Products.tsx":
/*!*************************************!*\
  !*** ./src/components/Products.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Products: function() { return /* binding */ Products; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _ui_Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* __next_internal_client_entry_do_not_use__ Products auto */ \nvar _s = $RefreshSig$();\n\n\n\n// Netflix-style categories\nconst productCategories = [\n    {\n        id: \"trending\",\n        name: \"Trending Now\",\n        description: \"Hot products everyone is buying\"\n    },\n    {\n        id: \"consoles\",\n        name: \"Gaming Consoles\",\n        description: \"Latest gaming systems\"\n    },\n    {\n        id: \"popular\",\n        name: \"Popular This Week\",\n        description: \"Most loved by gamers\"\n    },\n    {\n        id: \"accessories\",\n        name: \"Gaming Accessories\",\n        description: \"Enhance your gaming setup\"\n    },\n    {\n        id: \"games\",\n        name: \"Latest Games\",\n        description: \"New releases and classics\"\n    }\n];\nconst products = [\n    // Trending Products\n    {\n        id: 1,\n        title: \"PlayStation 5 Console\",\n        price: \"ETB 45,000\",\n        originalPrice: \"ETB 50,000\",\n        discount: \"10\",\n        rating: 5,\n        image: \"/images/device1.jpg\",\n        category: \"trending\",\n        type: \"consoles\"\n    },\n    {\n        id: 2,\n        title: \"Xbox Series X\",\n        price: \"ETB 42,000\",\n        originalPrice: \"ETB 47,000\",\n        discount: \"11\",\n        rating: 5,\n        image: \"/images/xbox.jpg\",\n        category: \"trending\",\n        type: \"consoles\"\n    },\n    {\n        id: 3,\n        title: \"Nintendo Switch OLED\",\n        price: \"ETB 28,000\",\n        originalPrice: \"ETB 32,000\",\n        discount: \"13\",\n        rating: 4,\n        image: \"/images/device3.jpg\",\n        category: \"trending\",\n        type: \"consoles\"\n    },\n    // Popular Products\n    {\n        id: 4,\n        title: \"DualSense Wireless Controller\",\n        price: \"ETB 4,500\",\n        originalPrice: \"ETB 5,200\",\n        discount: \"13\",\n        rating: 5,\n        image: \"/images/device4.jpg\",\n        category: \"popular\",\n        type: \"accessories\"\n    },\n    {\n        id: 5,\n        title: \"Gaming Headset Pro\",\n        price: \"ETB 3,200\",\n        originalPrice: \"ETB 4,000\",\n        discount: \"20\",\n        rating: 4,\n        image: \"/images/device5.jpg\",\n        category: \"popular\",\n        type: \"accessories\"\n    },\n    {\n        id: 6,\n        title: \"Mechanical Gaming Keyboard\",\n        price: \"ETB 2,800\",\n        originalPrice: \"ETB 3,500\",\n        discount: \"20\",\n        rating: 4,\n        image: \"/images/device6.jpg\",\n        category: \"popular\",\n        type: \"accessories\"\n    },\n    // Games\n    {\n        id: 7,\n        title: \"Call of Duty: Modern Warfare III\",\n        price: \"ETB 3,500\",\n        originalPrice: \"ETB 4,200\",\n        discount: \"17\",\n        rating: 5,\n        image: \"/images/\\uD83D\\uDD2BCall of duty.jpg\",\n        category: \"popular\",\n        type: \"games\"\n    },\n    {\n        id: 8,\n        title: \"FIFA 24\",\n        price: \"ETB 3,200\",\n        originalPrice: \"ETB 3,800\",\n        discount: \"16\",\n        rating: 4,\n        image: \"/images/gl5.jpg\",\n        category: \"trending\",\n        type: \"games\"\n    },\n    // Additional Games\n    {\n        id: 9,\n        title: \"Resident Evil 7\",\n        price: \"ETB 2,800\",\n        originalPrice: \"ETB 3,200\",\n        discount: \"13\",\n        rating: 5,\n        image: \"/images/re7.jpg\",\n        category: \"popular\",\n        type: \"games\"\n    },\n    {\n        id: 10,\n        title: \"Grand Theft Auto V\",\n        price: \"ETB 2,500\",\n        originalPrice: \"ETB 3,000\",\n        discount: \"17\",\n        rating: 5,\n        image: \"/images/gl6.jpg\",\n        category: \"popular\",\n        type: \"games\"\n    },\n    {\n        id: 11,\n        title: \"Cyberpunk 2077\",\n        price: \"ETB 3,800\",\n        originalPrice: \"ETB 4,500\",\n        discount: \"16\",\n        rating: 4,\n        image: \"/images/gl7.jpg\",\n        category: \"trending\",\n        type: \"games\"\n    },\n    {\n        id: 12,\n        title: 'Gaming Monitor 27\"',\n        price: \"ETB 15,000\",\n        originalPrice: \"ETB 18,000\",\n        discount: \"17\",\n        rating: 5,\n        image: \"/images/v3.jpg\",\n        category: \"popular\",\n        type: \"accessories\"\n    }\n];\nconst Products = ()=>{\n    _s();\n    const [activeCategory, setActiveCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"grid\");\n    const filteredProducts = activeCategory === \"all\" ? products : products.filter((product)=>product.category === activeCategory || product.type === activeCategory);\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.1\n            }\n        }\n    };\n    const itemVariants = {\n        hidden: {\n            opacity: 0,\n            y: 20\n        },\n        visible: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.5\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"products\",\n        className: \"py-20 bg-gray-900/50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.6\n                    },\n                    className: \"text-center mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl md:text-5xl font-bold text-white mb-4\",\n                            children: [\n                                \"Our \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"gradient-text\",\n                                    children: \"Products\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\legends\\\\legends\\\\src\\\\components\\\\Products.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\legends\\\\legends\\\\src\\\\components\\\\Products.tsx\",\n                            lineNumber: 199,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-400 max-w-2xl mx-auto\",\n                            children: \"Discover our extensive collection of gaming consoles, accessories, and the latest games\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\legends\\\\legends\\\\src\\\\components\\\\Products.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\legends\\\\legends\\\\src\\\\components\\\\Products.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.6,\n                        delay: 0.2\n                    },\n                    className: \"flex flex-wrap justify-center gap-4 mb-8\",\n                    children: productCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActiveCategory(category.id),\n                            className: \"px-6 py-3 rounded-full font-medium transition-all duration-300 \".concat(activeCategory === category.id ? \"bg-blue-600 text-white shadow-lg\" : \"bg-gray-800 text-gray-300 hover:bg-gray-700 hover:text-white\"),\n                            children: [\n                                category.name,\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-2 text-sm opacity-75\",\n                                    children: [\n                                        \"(\",\n                                        category.count,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\legends\\\\legends\\\\src\\\\components\\\\Products.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, category.id, true, {\n                            fileName: \"D:\\\\legends\\\\legends\\\\src\\\\components\\\\Products.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"D:\\\\legends\\\\legends\\\\src\\\\components\\\\Products.tsx\",\n                    lineNumber: 208,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.6,\n                        delay: 0.3\n                    },\n                    className: \"flex items-center justify-between mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Filter, {\n                                    className: \"h-5 w-5 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\legends\\\\legends\\\\src\\\\components\\\\Products.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-400\",\n                                    children: [\n                                        \"Showing \",\n                                        filteredProducts.length,\n                                        \" products\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\legends\\\\legends\\\\src\\\\components\\\\Products.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\legends\\\\legends\\\\src\\\\components\\\\Products.tsx\",\n                            lineNumber: 239,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setViewMode(\"grid\"),\n                                    className: \"p-2 rounded-lg transition-colors duration-300 \".concat(viewMode === \"grid\" ? \"bg-blue-600 text-white\" : \"bg-gray-800 text-gray-400\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Grid, {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\legends\\\\legends\\\\src\\\\components\\\\Products.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\legends\\\\legends\\\\src\\\\components\\\\Products.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setViewMode(\"list\"),\n                                    className: \"p-2 rounded-lg transition-colors duration-300 \".concat(viewMode === \"list\" ? \"bg-blue-600 text-white\" : \"bg-gray-800 text-gray-400\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(List, {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\legends\\\\legends\\\\src\\\\components\\\\Products.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\legends\\\\legends\\\\src\\\\components\\\\Products.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\legends\\\\legends\\\\src\\\\components\\\\Products.tsx\",\n                            lineNumber: 246,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\legends\\\\legends\\\\src\\\\components\\\\Products.tsx\",\n                    lineNumber: 232,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    variants: containerVariants,\n                    initial: \"hidden\",\n                    whileInView: \"visible\",\n                    viewport: {\n                        once: true\n                    },\n                    className: \"grid gap-6 \".concat(viewMode === \"grid\" ? \"grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4\" : \"grid-cols-1\"),\n                    children: filteredProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            variants: itemVariants,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductCard, {\n                                title: product.title,\n                                price: product.price,\n                                originalPrice: product.originalPrice,\n                                discount: product.discount,\n                                rating: product.rating,\n                                image: product.image,\n                                category: product.category,\n                                onClick: ()=>console.log(\"Product clicked:\", product.id)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\legends\\\\legends\\\\src\\\\components\\\\Products.tsx\",\n                                lineNumber: 280,\n                                columnNumber: 15\n                            }, undefined)\n                        }, product.id, false, {\n                            fileName: \"D:\\\\legends\\\\legends\\\\src\\\\components\\\\Products.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"D:\\\\legends\\\\legends\\\\src\\\\components\\\\Products.tsx\",\n                    lineNumber: 267,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.6,\n                        delay: 0.4\n                    },\n                    className: \"text-center mt-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        variant: \"outline\",\n                        size: \"lg\",\n                        children: \"Load More Products\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\legends\\\\legends\\\\src\\\\components\\\\Products.tsx\",\n                        lineNumber: 302,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\legends\\\\legends\\\\src\\\\components\\\\Products.tsx\",\n                    lineNumber: 295,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\legends\\\\legends\\\\src\\\\components\\\\Products.tsx\",\n            lineNumber: 190,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\legends\\\\legends\\\\src\\\\components\\\\Products.tsx\",\n        lineNumber: 189,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Products, \"d8Uk5XcjjH/DLyQddN99upbGdyY=\");\n_c = Products;\nvar _c;\n$RefreshReg$(_c, \"Products\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL1Byb2R1Y3RzLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUVnRDtBQUNUO0FBRUY7QUFFckMsMkJBQTJCO0FBQzNCLE1BQU1JLG9CQUFvQjtJQUN4QjtRQUFFQyxJQUFJO1FBQVlDLE1BQU07UUFBZ0JDLGFBQWE7SUFBa0M7SUFDdkY7UUFBRUYsSUFBSTtRQUFZQyxNQUFNO1FBQW1CQyxhQUFhO0lBQXdCO0lBQ2hGO1FBQUVGLElBQUk7UUFBV0MsTUFBTTtRQUFxQkMsYUFBYTtJQUF1QjtJQUNoRjtRQUFFRixJQUFJO1FBQWVDLE1BQU07UUFBc0JDLGFBQWE7SUFBNEI7SUFDMUY7UUFBRUYsSUFBSTtRQUFTQyxNQUFNO1FBQWdCQyxhQUFhO0lBQTRCO0NBQy9FO0FBRUQsTUFBTUMsV0FBVztJQUNmLG9CQUFvQjtJQUNwQjtRQUNFSCxJQUFJO1FBQ0pJLE9BQU87UUFDUEMsT0FBTztRQUNQQyxlQUFlO1FBQ2ZDLFVBQVU7UUFDVkMsUUFBUTtRQUNSQyxPQUFPO1FBQ1BDLFVBQVU7UUFDVkMsTUFBTTtJQUNSO0lBQ0E7UUFDRVgsSUFBSTtRQUNKSSxPQUFPO1FBQ1BDLE9BQU87UUFDUEMsZUFBZTtRQUNmQyxVQUFVO1FBQ1ZDLFFBQVE7UUFDUkMsT0FBTztRQUNQQyxVQUFVO1FBQ1ZDLE1BQU07SUFDUjtJQUNBO1FBQ0VYLElBQUk7UUFDSkksT0FBTztRQUNQQyxPQUFPO1FBQ1BDLGVBQWU7UUFDZkMsVUFBVTtRQUNWQyxRQUFRO1FBQ1JDLE9BQU87UUFDUEMsVUFBVTtRQUNWQyxNQUFNO0lBQ1I7SUFFQSxtQkFBbUI7SUFDbkI7UUFDRVgsSUFBSTtRQUNKSSxPQUFPO1FBQ1BDLE9BQU87UUFDUEMsZUFBZTtRQUNmQyxVQUFVO1FBQ1ZDLFFBQVE7UUFDUkMsT0FBTztRQUNQQyxVQUFVO1FBQ1ZDLE1BQU07SUFDUjtJQUNBO1FBQ0VYLElBQUk7UUFDSkksT0FBTztRQUNQQyxPQUFPO1FBQ1BDLGVBQWU7UUFDZkMsVUFBVTtRQUNWQyxRQUFRO1FBQ1JDLE9BQU87UUFDUEMsVUFBVTtRQUNWQyxNQUFNO0lBQ1I7SUFDQTtRQUNFWCxJQUFJO1FBQ0pJLE9BQU87UUFDUEMsT0FBTztRQUNQQyxlQUFlO1FBQ2ZDLFVBQVU7UUFDVkMsUUFBUTtRQUNSQyxPQUFPO1FBQ1BDLFVBQVU7UUFDVkMsTUFBTTtJQUNSO0lBRUEsUUFBUTtJQUNSO1FBQ0VYLElBQUk7UUFDSkksT0FBTztRQUNQQyxPQUFPO1FBQ1BDLGVBQWU7UUFDZkMsVUFBVTtRQUNWQyxRQUFRO1FBQ1JDLE9BQU87UUFDUEMsVUFBVTtRQUNWQyxNQUFNO0lBQ1I7SUFDQTtRQUNFWCxJQUFJO1FBQ0pJLE9BQU87UUFDUEMsT0FBTztRQUNQQyxlQUFlO1FBQ2ZDLFVBQVU7UUFDVkMsUUFBUTtRQUNSQyxPQUFPO1FBQ1BDLFVBQVU7UUFDVkMsTUFBTTtJQUNSO0lBRUEsbUJBQW1CO0lBQ25CO1FBQ0VYLElBQUk7UUFDSkksT0FBTztRQUNQQyxPQUFPO1FBQ1BDLGVBQWU7UUFDZkMsVUFBVTtRQUNWQyxRQUFRO1FBQ1JDLE9BQU87UUFDUEMsVUFBVTtRQUNWQyxNQUFNO0lBQ1I7SUFDQTtRQUNFWCxJQUFJO1FBQ0pJLE9BQU87UUFDUEMsT0FBTztRQUNQQyxlQUFlO1FBQ2ZDLFVBQVU7UUFDVkMsUUFBUTtRQUNSQyxPQUFPO1FBQ1BDLFVBQVU7UUFDVkMsTUFBTTtJQUNSO0lBQ0E7UUFDRVgsSUFBSTtRQUNKSSxPQUFPO1FBQ1BDLE9BQU87UUFDUEMsZUFBZTtRQUNmQyxVQUFVO1FBQ1ZDLFFBQVE7UUFDUkMsT0FBTztRQUNQQyxVQUFVO1FBQ1ZDLE1BQU07SUFDUjtJQUNBO1FBQ0VYLElBQUk7UUFDSkksT0FBTztRQUNQQyxPQUFPO1FBQ1BDLGVBQWU7UUFDZkMsVUFBVTtRQUNWQyxRQUFRO1FBQ1JDLE9BQU87UUFDUEMsVUFBVTtRQUNWQyxNQUFNO0lBQ1I7Q0FDRDtBQUVNLE1BQU1DLFdBQXFCOztJQUNoQyxNQUFNLENBQUNDLGdCQUFnQkMsa0JBQWtCLEdBQUdsQiwrQ0FBUUEsQ0FBQztJQUNyRCxNQUFNLENBQUNtQixVQUFVQyxZQUFZLEdBQUdwQiwrQ0FBUUEsQ0FBa0I7SUFFMUQsTUFBTXFCLG1CQUFtQkosbUJBQW1CLFFBQ3hDVixXQUNBQSxTQUFTZSxNQUFNLENBQUNDLENBQUFBLFVBQVdBLFFBQVFULFFBQVEsS0FBS0csa0JBQWtCTSxRQUFRUixJQUFJLEtBQUtFO0lBRXZGLE1BQU1PLG9CQUFvQjtRQUN4QkMsUUFBUTtZQUFFQyxTQUFTO1FBQUU7UUFDckJDLFNBQVM7WUFDUEQsU0FBUztZQUNURSxZQUFZO2dCQUNWQyxpQkFBaUI7WUFDbkI7UUFDRjtJQUNGO0lBRUEsTUFBTUMsZUFBZTtRQUNuQkwsUUFBUTtZQUFFQyxTQUFTO1lBQUdLLEdBQUc7UUFBRztRQUM1QkosU0FBUztZQUNQRCxTQUFTO1lBQ1RLLEdBQUc7WUFDSEgsWUFBWTtnQkFDVkksVUFBVTtZQUNaO1FBQ0Y7SUFDRjtJQUVBLHFCQUNFLDhEQUFDQztRQUFRN0IsSUFBRztRQUFXOEIsV0FBVTtrQkFDL0IsNEVBQUNDO1lBQUlELFdBQVU7OzhCQUViLDhEQUFDakMsaURBQU1BLENBQUNrQyxHQUFHO29CQUNUQyxTQUFTO3dCQUFFVixTQUFTO3dCQUFHSyxHQUFHO29CQUFHO29CQUM3Qk0sYUFBYTt3QkFBRVgsU0FBUzt3QkFBR0ssR0FBRztvQkFBRTtvQkFDaENPLFVBQVU7d0JBQUVDLE1BQU07b0JBQUs7b0JBQ3ZCWCxZQUFZO3dCQUFFSSxVQUFVO29CQUFJO29CQUM1QkUsV0FBVTs7c0NBRVYsOERBQUNNOzRCQUFHTixXQUFVOztnQ0FBaUQ7OENBQ3pELDhEQUFDTztvQ0FBS1AsV0FBVTs4Q0FBZ0I7Ozs7Ozs7Ozs7OztzQ0FFdEMsOERBQUNROzRCQUFFUixXQUFVO3NDQUEwQzs7Ozs7Ozs7Ozs7OzhCQU16RCw4REFBQ2pDLGlEQUFNQSxDQUFDa0MsR0FBRztvQkFDVEMsU0FBUzt3QkFBRVYsU0FBUzt3QkFBR0ssR0FBRztvQkFBRztvQkFDN0JNLGFBQWE7d0JBQUVYLFNBQVM7d0JBQUdLLEdBQUc7b0JBQUU7b0JBQ2hDTyxVQUFVO3dCQUFFQyxNQUFNO29CQUFLO29CQUN2QlgsWUFBWTt3QkFBRUksVUFBVTt3QkFBS1csT0FBTztvQkFBSTtvQkFDeENULFdBQVU7OEJBRVQvQixrQkFBa0J5QyxHQUFHLENBQUMsQ0FBQzlCLHlCQUN0Qiw4REFBQytCOzRCQUVDQyxTQUFTLElBQU01QixrQkFBa0JKLFNBQVNWLEVBQUU7NEJBQzVDOEIsV0FBVyxrRUFJVixPQUhDakIsbUJBQW1CSCxTQUFTVixFQUFFLEdBQzFCLHFDQUNBOztnQ0FHTFUsU0FBU1QsSUFBSTs4Q0FDZCw4REFBQ29DO29DQUFLUCxXQUFVOzt3Q0FBMEI7d0NBQUVwQixTQUFTaUMsS0FBSzt3Q0FBQzs7Ozs7Ozs7MkJBVHREakMsU0FBU1YsRUFBRTs7Ozs7Ozs7Ozs4QkFldEIsOERBQUNILGlEQUFNQSxDQUFDa0MsR0FBRztvQkFDVEMsU0FBUzt3QkFBRVYsU0FBUzt3QkFBR0ssR0FBRztvQkFBRztvQkFDN0JNLGFBQWE7d0JBQUVYLFNBQVM7d0JBQUdLLEdBQUc7b0JBQUU7b0JBQ2hDTyxVQUFVO3dCQUFFQyxNQUFNO29CQUFLO29CQUN2QlgsWUFBWTt3QkFBRUksVUFBVTt3QkFBS1csT0FBTztvQkFBSTtvQkFDeENULFdBQVU7O3NDQUVWLDhEQUFDQzs0QkFBSUQsV0FBVTs7OENBQ2IsOERBQUNjO29DQUFPZCxXQUFVOzs7Ozs7OENBQ2xCLDhEQUFDTztvQ0FBS1AsV0FBVTs7d0NBQWdCO3dDQUNyQmIsaUJBQWlCNEIsTUFBTTt3Q0FBQzs7Ozs7Ozs7Ozs7OztzQ0FJckMsOERBQUNkOzRCQUFJRCxXQUFVOzs4Q0FDYiw4REFBQ1c7b0NBQ0NDLFNBQVMsSUFBTTFCLFlBQVk7b0NBQzNCYyxXQUFXLGlEQUVWLE9BRENmLGFBQWEsU0FBUywyQkFBMkI7OENBR25ELDRFQUFDK0I7d0NBQUtoQixXQUFVOzs7Ozs7Ozs7Ozs4Q0FFbEIsOERBQUNXO29DQUNDQyxTQUFTLElBQU0xQixZQUFZO29DQUMzQmMsV0FBVyxpREFFVixPQURDZixhQUFhLFNBQVMsMkJBQTJCOzhDQUduRCw0RUFBQ2dDO3dDQUFLakIsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBTXRCLDhEQUFDakMsaURBQU1BLENBQUNrQyxHQUFHO29CQUNUaUIsVUFBVTVCO29CQUNWWSxTQUFRO29CQUNSQyxhQUFZO29CQUNaQyxVQUFVO3dCQUFFQyxNQUFNO29CQUFLO29CQUN2QkwsV0FBVyxjQUlWLE9BSENmLGFBQWEsU0FDVCw2REFDQTs4QkFHTEUsaUJBQWlCdUIsR0FBRyxDQUFDLENBQUNyQix3QkFDckIsOERBQUN0QixpREFBTUEsQ0FBQ2tDLEdBQUc7NEJBQWtCaUIsVUFBVXRCO3NDQUNyQyw0RUFBQ3VCO2dDQUNDN0MsT0FBT2UsUUFBUWYsS0FBSztnQ0FDcEJDLE9BQU9jLFFBQVFkLEtBQUs7Z0NBQ3BCQyxlQUFlYSxRQUFRYixhQUFhO2dDQUNwQ0MsVUFBVVksUUFBUVosUUFBUTtnQ0FDMUJDLFFBQVFXLFFBQVFYLE1BQU07Z0NBQ3RCQyxPQUFPVSxRQUFRVixLQUFLO2dDQUNwQkMsVUFBVVMsUUFBUVQsUUFBUTtnQ0FDMUJnQyxTQUFTLElBQU1RLFFBQVFDLEdBQUcsQ0FBQyxvQkFBb0JoQyxRQUFRbkIsRUFBRTs7Ozs7OzJCQVQ1Q21CLFFBQVFuQixFQUFFOzs7Ozs7Ozs7OzhCQWdCL0IsOERBQUNILGlEQUFNQSxDQUFDa0MsR0FBRztvQkFDVEMsU0FBUzt3QkFBRVYsU0FBUzt3QkFBR0ssR0FBRztvQkFBRztvQkFDN0JNLGFBQWE7d0JBQUVYLFNBQVM7d0JBQUdLLEdBQUc7b0JBQUU7b0JBQ2hDTyxVQUFVO3dCQUFFQyxNQUFNO29CQUFLO29CQUN2QlgsWUFBWTt3QkFBRUksVUFBVTt3QkFBS1csT0FBTztvQkFBSTtvQkFDeENULFdBQVU7OEJBRVYsNEVBQUNoQyw4Q0FBTUE7d0JBQUNzRCxTQUFRO3dCQUFVQyxNQUFLO2tDQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTzlDLEVBQUU7R0F0Sld6QztLQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvY29tcG9uZW50cy9Qcm9kdWN0cy50c3g/YmE3ZCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlUmVmIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgbW90aW9uIH0gZnJvbSAnZnJhbWVyLW1vdGlvbic7XG5pbXBvcnQgeyBDaGV2cm9uTGVmdCwgQ2hldnJvblJpZ2h0LCBTdGFyLCBQbGF5LCBJbmZvLCBQbHVzIH0gZnJvbSAnbHVjaWRlLXJlYWN0JztcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gJy4vdWkvQnV0dG9uJztcblxuLy8gTmV0ZmxpeC1zdHlsZSBjYXRlZ29yaWVzXG5jb25zdCBwcm9kdWN0Q2F0ZWdvcmllcyA9IFtcbiAgeyBpZDogJ3RyZW5kaW5nJywgbmFtZTogJ1RyZW5kaW5nIE5vdycsIGRlc2NyaXB0aW9uOiAnSG90IHByb2R1Y3RzIGV2ZXJ5b25lIGlzIGJ1eWluZycgfSxcbiAgeyBpZDogJ2NvbnNvbGVzJywgbmFtZTogJ0dhbWluZyBDb25zb2xlcycsIGRlc2NyaXB0aW9uOiAnTGF0ZXN0IGdhbWluZyBzeXN0ZW1zJyB9LFxuICB7IGlkOiAncG9wdWxhcicsIG5hbWU6ICdQb3B1bGFyIFRoaXMgV2VlaycsIGRlc2NyaXB0aW9uOiAnTW9zdCBsb3ZlZCBieSBnYW1lcnMnIH0sXG4gIHsgaWQ6ICdhY2Nlc3NvcmllcycsIG5hbWU6ICdHYW1pbmcgQWNjZXNzb3JpZXMnLCBkZXNjcmlwdGlvbjogJ0VuaGFuY2UgeW91ciBnYW1pbmcgc2V0dXAnIH0sXG4gIHsgaWQ6ICdnYW1lcycsIG5hbWU6ICdMYXRlc3QgR2FtZXMnLCBkZXNjcmlwdGlvbjogJ05ldyByZWxlYXNlcyBhbmQgY2xhc3NpY3MnIH1cbl07XG5cbmNvbnN0IHByb2R1Y3RzID0gW1xuICAvLyBUcmVuZGluZyBQcm9kdWN0c1xuICB7XG4gICAgaWQ6IDEsXG4gICAgdGl0bGU6IFwiUGxheVN0YXRpb24gNSBDb25zb2xlXCIsXG4gICAgcHJpY2U6IFwiRVRCIDQ1LDAwMFwiLFxuICAgIG9yaWdpbmFsUHJpY2U6IFwiRVRCIDUwLDAwMFwiLFxuICAgIGRpc2NvdW50OiBcIjEwXCIsXG4gICAgcmF0aW5nOiA1LFxuICAgIGltYWdlOiBcIi9pbWFnZXMvZGV2aWNlMS5qcGdcIixcbiAgICBjYXRlZ29yeTogXCJ0cmVuZGluZ1wiLFxuICAgIHR5cGU6IFwiY29uc29sZXNcIlxuICB9LFxuICB7XG4gICAgaWQ6IDIsXG4gICAgdGl0bGU6IFwiWGJveCBTZXJpZXMgWFwiLFxuICAgIHByaWNlOiBcIkVUQiA0MiwwMDBcIixcbiAgICBvcmlnaW5hbFByaWNlOiBcIkVUQiA0NywwMDBcIixcbiAgICBkaXNjb3VudDogXCIxMVwiLFxuICAgIHJhdGluZzogNSxcbiAgICBpbWFnZTogXCIvaW1hZ2VzL3hib3guanBnXCIsXG4gICAgY2F0ZWdvcnk6IFwidHJlbmRpbmdcIixcbiAgICB0eXBlOiBcImNvbnNvbGVzXCJcbiAgfSxcbiAge1xuICAgIGlkOiAzLFxuICAgIHRpdGxlOiBcIk5pbnRlbmRvIFN3aXRjaCBPTEVEXCIsXG4gICAgcHJpY2U6IFwiRVRCIDI4LDAwMFwiLFxuICAgIG9yaWdpbmFsUHJpY2U6IFwiRVRCIDMyLDAwMFwiLFxuICAgIGRpc2NvdW50OiBcIjEzXCIsXG4gICAgcmF0aW5nOiA0LFxuICAgIGltYWdlOiBcIi9pbWFnZXMvZGV2aWNlMy5qcGdcIixcbiAgICBjYXRlZ29yeTogXCJ0cmVuZGluZ1wiLFxuICAgIHR5cGU6IFwiY29uc29sZXNcIlxuICB9LFxuXG4gIC8vIFBvcHVsYXIgUHJvZHVjdHNcbiAge1xuICAgIGlkOiA0LFxuICAgIHRpdGxlOiBcIkR1YWxTZW5zZSBXaXJlbGVzcyBDb250cm9sbGVyXCIsXG4gICAgcHJpY2U6IFwiRVRCIDQsNTAwXCIsXG4gICAgb3JpZ2luYWxQcmljZTogXCJFVEIgNSwyMDBcIixcbiAgICBkaXNjb3VudDogXCIxM1wiLFxuICAgIHJhdGluZzogNSxcbiAgICBpbWFnZTogXCIvaW1hZ2VzL2RldmljZTQuanBnXCIsXG4gICAgY2F0ZWdvcnk6IFwicG9wdWxhclwiLFxuICAgIHR5cGU6IFwiYWNjZXNzb3JpZXNcIlxuICB9LFxuICB7XG4gICAgaWQ6IDUsXG4gICAgdGl0bGU6IFwiR2FtaW5nIEhlYWRzZXQgUHJvXCIsXG4gICAgcHJpY2U6IFwiRVRCIDMsMjAwXCIsXG4gICAgb3JpZ2luYWxQcmljZTogXCJFVEIgNCwwMDBcIixcbiAgICBkaXNjb3VudDogXCIyMFwiLFxuICAgIHJhdGluZzogNCxcbiAgICBpbWFnZTogXCIvaW1hZ2VzL2RldmljZTUuanBnXCIsXG4gICAgY2F0ZWdvcnk6IFwicG9wdWxhclwiLFxuICAgIHR5cGU6IFwiYWNjZXNzb3JpZXNcIlxuICB9LFxuICB7XG4gICAgaWQ6IDYsXG4gICAgdGl0bGU6IFwiTWVjaGFuaWNhbCBHYW1pbmcgS2V5Ym9hcmRcIixcbiAgICBwcmljZTogXCJFVEIgMiw4MDBcIixcbiAgICBvcmlnaW5hbFByaWNlOiBcIkVUQiAzLDUwMFwiLFxuICAgIGRpc2NvdW50OiBcIjIwXCIsXG4gICAgcmF0aW5nOiA0LFxuICAgIGltYWdlOiBcIi9pbWFnZXMvZGV2aWNlNi5qcGdcIixcbiAgICBjYXRlZ29yeTogXCJwb3B1bGFyXCIsXG4gICAgdHlwZTogXCJhY2Nlc3Nvcmllc1wiXG4gIH0sXG5cbiAgLy8gR2FtZXNcbiAge1xuICAgIGlkOiA3LFxuICAgIHRpdGxlOiBcIkNhbGwgb2YgRHV0eTogTW9kZXJuIFdhcmZhcmUgSUlJXCIsXG4gICAgcHJpY2U6IFwiRVRCIDMsNTAwXCIsXG4gICAgb3JpZ2luYWxQcmljZTogXCJFVEIgNCwyMDBcIixcbiAgICBkaXNjb3VudDogXCIxN1wiLFxuICAgIHJhdGluZzogNSxcbiAgICBpbWFnZTogXCIvaW1hZ2VzL/CflKtDYWxsIG9mIGR1dHkuanBnXCIsXG4gICAgY2F0ZWdvcnk6IFwicG9wdWxhclwiLFxuICAgIHR5cGU6IFwiZ2FtZXNcIlxuICB9LFxuICB7XG4gICAgaWQ6IDgsXG4gICAgdGl0bGU6IFwiRklGQSAyNFwiLFxuICAgIHByaWNlOiBcIkVUQiAzLDIwMFwiLFxuICAgIG9yaWdpbmFsUHJpY2U6IFwiRVRCIDMsODAwXCIsXG4gICAgZGlzY291bnQ6IFwiMTZcIixcbiAgICByYXRpbmc6IDQsXG4gICAgaW1hZ2U6IFwiL2ltYWdlcy9nbDUuanBnXCIsXG4gICAgY2F0ZWdvcnk6IFwidHJlbmRpbmdcIixcbiAgICB0eXBlOiBcImdhbWVzXCJcbiAgfSxcblxuICAvLyBBZGRpdGlvbmFsIEdhbWVzXG4gIHtcbiAgICBpZDogOSxcbiAgICB0aXRsZTogXCJSZXNpZGVudCBFdmlsIDdcIixcbiAgICBwcmljZTogXCJFVEIgMiw4MDBcIixcbiAgICBvcmlnaW5hbFByaWNlOiBcIkVUQiAzLDIwMFwiLFxuICAgIGRpc2NvdW50OiBcIjEzXCIsXG4gICAgcmF0aW5nOiA1LFxuICAgIGltYWdlOiBcIi9pbWFnZXMvcmU3LmpwZ1wiLFxuICAgIGNhdGVnb3J5OiBcInBvcHVsYXJcIixcbiAgICB0eXBlOiBcImdhbWVzXCJcbiAgfSxcbiAge1xuICAgIGlkOiAxMCxcbiAgICB0aXRsZTogXCJHcmFuZCBUaGVmdCBBdXRvIFZcIixcbiAgICBwcmljZTogXCJFVEIgMiw1MDBcIixcbiAgICBvcmlnaW5hbFByaWNlOiBcIkVUQiAzLDAwMFwiLFxuICAgIGRpc2NvdW50OiBcIjE3XCIsXG4gICAgcmF0aW5nOiA1LFxuICAgIGltYWdlOiBcIi9pbWFnZXMvZ2w2LmpwZ1wiLFxuICAgIGNhdGVnb3J5OiBcInBvcHVsYXJcIixcbiAgICB0eXBlOiBcImdhbWVzXCJcbiAgfSxcbiAge1xuICAgIGlkOiAxMSxcbiAgICB0aXRsZTogXCJDeWJlcnB1bmsgMjA3N1wiLFxuICAgIHByaWNlOiBcIkVUQiAzLDgwMFwiLFxuICAgIG9yaWdpbmFsUHJpY2U6IFwiRVRCIDQsNTAwXCIsXG4gICAgZGlzY291bnQ6IFwiMTZcIixcbiAgICByYXRpbmc6IDQsXG4gICAgaW1hZ2U6IFwiL2ltYWdlcy9nbDcuanBnXCIsXG4gICAgY2F0ZWdvcnk6IFwidHJlbmRpbmdcIixcbiAgICB0eXBlOiBcImdhbWVzXCJcbiAgfSxcbiAge1xuICAgIGlkOiAxMixcbiAgICB0aXRsZTogXCJHYW1pbmcgTW9uaXRvciAyN1xcXCJcIixcbiAgICBwcmljZTogXCJFVEIgMTUsMDAwXCIsXG4gICAgb3JpZ2luYWxQcmljZTogXCJFVEIgMTgsMDAwXCIsXG4gICAgZGlzY291bnQ6IFwiMTdcIixcbiAgICByYXRpbmc6IDUsXG4gICAgaW1hZ2U6IFwiL2ltYWdlcy92My5qcGdcIixcbiAgICBjYXRlZ29yeTogXCJwb3B1bGFyXCIsXG4gICAgdHlwZTogXCJhY2Nlc3Nvcmllc1wiXG4gIH1cbl07XG5cbmV4cG9ydCBjb25zdCBQcm9kdWN0czogUmVhY3QuRkMgPSAoKSA9PiB7XG4gIGNvbnN0IFthY3RpdmVDYXRlZ29yeSwgc2V0QWN0aXZlQ2F0ZWdvcnldID0gdXNlU3RhdGUoJ2FsbCcpO1xuICBjb25zdCBbdmlld01vZGUsIHNldFZpZXdNb2RlXSA9IHVzZVN0YXRlPCdncmlkJyB8ICdsaXN0Jz4oJ2dyaWQnKTtcblxuICBjb25zdCBmaWx0ZXJlZFByb2R1Y3RzID0gYWN0aXZlQ2F0ZWdvcnkgPT09ICdhbGwnXG4gICAgPyBwcm9kdWN0c1xuICAgIDogcHJvZHVjdHMuZmlsdGVyKHByb2R1Y3QgPT4gcHJvZHVjdC5jYXRlZ29yeSA9PT0gYWN0aXZlQ2F0ZWdvcnkgfHwgcHJvZHVjdC50eXBlID09PSBhY3RpdmVDYXRlZ29yeSk7XG5cbiAgY29uc3QgY29udGFpbmVyVmFyaWFudHMgPSB7XG4gICAgaGlkZGVuOiB7IG9wYWNpdHk6IDAgfSxcbiAgICB2aXNpYmxlOiB7XG4gICAgICBvcGFjaXR5OiAxLFxuICAgICAgdHJhbnNpdGlvbjoge1xuICAgICAgICBzdGFnZ2VyQ2hpbGRyZW46IDAuMVxuICAgICAgfVxuICAgIH1cbiAgfTtcblxuICBjb25zdCBpdGVtVmFyaWFudHMgPSB7XG4gICAgaGlkZGVuOiB7IG9wYWNpdHk6IDAsIHk6IDIwIH0sXG4gICAgdmlzaWJsZToge1xuICAgICAgb3BhY2l0eTogMSxcbiAgICAgIHk6IDAsXG4gICAgICB0cmFuc2l0aW9uOiB7XG4gICAgICAgIGR1cmF0aW9uOiAwLjVcbiAgICAgIH1cbiAgICB9XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8c2VjdGlvbiBpZD1cInByb2R1Y3RzXCIgY2xhc3NOYW1lPVwicHktMjAgYmctZ3JheS05MDAvNTBcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyIG14LWF1dG8gcHgtNFwiPlxuICAgICAgICB7LyogU2VjdGlvbiBIZWFkZXIgKi99XG4gICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAzMCB9fVxuICAgICAgICAgIHdoaWxlSW5WaWV3PXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cbiAgICAgICAgICB2aWV3cG9ydD17eyBvbmNlOiB0cnVlIH19XG4gICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC42IH19XG4gICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgbWItMTJcIlxuICAgICAgICA+XG4gICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtNHhsIG1kOnRleHQtNXhsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlIG1iLTRcIj5cbiAgICAgICAgICAgIE91ciA8c3BhbiBjbGFzc05hbWU9XCJncmFkaWVudC10ZXh0XCI+UHJvZHVjdHM8L3NwYW4+XG4gICAgICAgICAgPC9oMj5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhsIHRleHQtZ3JheS00MDAgbWF4LXctMnhsIG14LWF1dG9cIj5cbiAgICAgICAgICAgIERpc2NvdmVyIG91ciBleHRlbnNpdmUgY29sbGVjdGlvbiBvZiBnYW1pbmcgY29uc29sZXMsIGFjY2Vzc29yaWVzLCBhbmQgdGhlIGxhdGVzdCBnYW1lc1xuICAgICAgICAgIDwvcD5cbiAgICAgICAgPC9tb3Rpb24uZGl2PlxuXG4gICAgICAgIHsvKiBDYXRlZ29yeSBGaWx0ZXIgKi99XG4gICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAyMCB9fVxuICAgICAgICAgIHdoaWxlSW5WaWV3PXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cbiAgICAgICAgICB2aWV3cG9ydD17eyBvbmNlOiB0cnVlIH19XG4gICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC42LCBkZWxheTogMC4yIH19XG4gICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBmbGV4LXdyYXAganVzdGlmeS1jZW50ZXIgZ2FwLTQgbWItOFwiXG4gICAgICAgID5cbiAgICAgICAgICB7cHJvZHVjdENhdGVnb3JpZXMubWFwKChjYXRlZ29yeSkgPT4gKFxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBrZXk9e2NhdGVnb3J5LmlkfVxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRBY3RpdmVDYXRlZ29yeShjYXRlZ29yeS5pZCl9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT17YHB4LTYgcHktMyByb3VuZGVkLWZ1bGwgZm9udC1tZWRpdW0gdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwICR7XG4gICAgICAgICAgICAgICAgYWN0aXZlQ2F0ZWdvcnkgPT09IGNhdGVnb3J5LmlkXG4gICAgICAgICAgICAgICAgICA/ICdiZy1ibHVlLTYwMCB0ZXh0LXdoaXRlIHNoYWRvdy1sZydcbiAgICAgICAgICAgICAgICAgIDogJ2JnLWdyYXktODAwIHRleHQtZ3JheS0zMDAgaG92ZXI6YmctZ3JheS03MDAgaG92ZXI6dGV4dC13aGl0ZSdcbiAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIHtjYXRlZ29yeS5uYW1lfVxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtbC0yIHRleHQtc20gb3BhY2l0eS03NVwiPih7Y2F0ZWdvcnkuY291bnR9KTwvc3Bhbj5cbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICkpfVxuICAgICAgICA8L21vdGlvbi5kaXY+XG5cbiAgICAgICAgey8qIFZpZXcgQ29udHJvbHMgKi99XG4gICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAyMCB9fVxuICAgICAgICAgIHdoaWxlSW5WaWV3PXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cbiAgICAgICAgICB2aWV3cG9ydD17eyBvbmNlOiB0cnVlIH19XG4gICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC42LCBkZWxheTogMC4zIH19XG4gICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIG1iLThcIlxuICAgICAgICA+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTRcIj5cbiAgICAgICAgICAgIDxGaWx0ZXIgY2xhc3NOYW1lPVwiaC01IHctNSB0ZXh0LWdyYXktNDAwXCIgLz5cbiAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDBcIj5cbiAgICAgICAgICAgICAgU2hvd2luZyB7ZmlsdGVyZWRQcm9kdWN0cy5sZW5ndGh9IHByb2R1Y3RzXG4gICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRWaWV3TW9kZSgnZ3JpZCcpfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2BwLTIgcm91bmRlZC1sZyB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0zMDAgJHtcbiAgICAgICAgICAgICAgICB2aWV3TW9kZSA9PT0gJ2dyaWQnID8gJ2JnLWJsdWUtNjAwIHRleHQtd2hpdGUnIDogJ2JnLWdyYXktODAwIHRleHQtZ3JheS00MDAnXG4gICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8R3JpZCBjbGFzc05hbWU9XCJoLTUgdy01XCIgLz5cbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRWaWV3TW9kZSgnbGlzdCcpfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2BwLTIgcm91bmRlZC1sZyB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0zMDAgJHtcbiAgICAgICAgICAgICAgICB2aWV3TW9kZSA9PT0gJ2xpc3QnID8gJ2JnLWJsdWUtNjAwIHRleHQtd2hpdGUnIDogJ2JnLWdyYXktODAwIHRleHQtZ3JheS00MDAnXG4gICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8TGlzdCBjbGFzc05hbWU9XCJoLTUgdy01XCIgLz5cbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L21vdGlvbi5kaXY+XG5cbiAgICAgICAgey8qIFByb2R1Y3RzIEdyaWQgKi99XG4gICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgdmFyaWFudHM9e2NvbnRhaW5lclZhcmlhbnRzfVxuICAgICAgICAgIGluaXRpYWw9XCJoaWRkZW5cIlxuICAgICAgICAgIHdoaWxlSW5WaWV3PVwidmlzaWJsZVwiXG4gICAgICAgICAgdmlld3BvcnQ9e3sgb25jZTogdHJ1ZSB9fVxuICAgICAgICAgIGNsYXNzTmFtZT17YGdyaWQgZ2FwLTYgJHtcbiAgICAgICAgICAgIHZpZXdNb2RlID09PSAnZ3JpZCdcbiAgICAgICAgICAgICAgPyAnZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgbGc6Z3JpZC1jb2xzLTMgeGw6Z3JpZC1jb2xzLTQnXG4gICAgICAgICAgICAgIDogJ2dyaWQtY29scy0xJ1xuICAgICAgICAgIH1gfVxuICAgICAgICA+XG4gICAgICAgICAge2ZpbHRlcmVkUHJvZHVjdHMubWFwKChwcm9kdWN0KSA9PiAoXG4gICAgICAgICAgICA8bW90aW9uLmRpdiBrZXk9e3Byb2R1Y3QuaWR9IHZhcmlhbnRzPXtpdGVtVmFyaWFudHN9PlxuICAgICAgICAgICAgICA8UHJvZHVjdENhcmRcbiAgICAgICAgICAgICAgICB0aXRsZT17cHJvZHVjdC50aXRsZX1cbiAgICAgICAgICAgICAgICBwcmljZT17cHJvZHVjdC5wcmljZX1cbiAgICAgICAgICAgICAgICBvcmlnaW5hbFByaWNlPXtwcm9kdWN0Lm9yaWdpbmFsUHJpY2V9XG4gICAgICAgICAgICAgICAgZGlzY291bnQ9e3Byb2R1Y3QuZGlzY291bnR9XG4gICAgICAgICAgICAgICAgcmF0aW5nPXtwcm9kdWN0LnJhdGluZ31cbiAgICAgICAgICAgICAgICBpbWFnZT17cHJvZHVjdC5pbWFnZX1cbiAgICAgICAgICAgICAgICBjYXRlZ29yeT17cHJvZHVjdC5jYXRlZ29yeX1cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBjb25zb2xlLmxvZygnUHJvZHVjdCBjbGlja2VkOicsIHByb2R1Y3QuaWQpfVxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICAgICkpfVxuICAgICAgICA8L21vdGlvbi5kaXY+XG5cbiAgICAgICAgey8qIExvYWQgTW9yZSBCdXR0b24gKi99XG4gICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAyMCB9fVxuICAgICAgICAgIHdoaWxlSW5WaWV3PXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cbiAgICAgICAgICB2aWV3cG9ydD17eyBvbmNlOiB0cnVlIH19XG4gICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC42LCBkZWxheTogMC40IH19XG4gICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgbXQtMTJcIlxuICAgICAgICA+XG4gICAgICAgICAgPEJ1dHRvbiB2YXJpYW50PVwib3V0bGluZVwiIHNpemU9XCJsZ1wiPlxuICAgICAgICAgICAgTG9hZCBNb3JlIFByb2R1Y3RzXG4gICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvc2VjdGlvbj5cbiAgKTtcbn07XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsIm1vdGlvbiIsIkJ1dHRvbiIsInByb2R1Y3RDYXRlZ29yaWVzIiwiaWQiLCJuYW1lIiwiZGVzY3JpcHRpb24iLCJwcm9kdWN0cyIsInRpdGxlIiwicHJpY2UiLCJvcmlnaW5hbFByaWNlIiwiZGlzY291bnQiLCJyYXRpbmciLCJpbWFnZSIsImNhdGVnb3J5IiwidHlwZSIsIlByb2R1Y3RzIiwiYWN0aXZlQ2F0ZWdvcnkiLCJzZXRBY3RpdmVDYXRlZ29yeSIsInZpZXdNb2RlIiwic2V0Vmlld01vZGUiLCJmaWx0ZXJlZFByb2R1Y3RzIiwiZmlsdGVyIiwicHJvZHVjdCIsImNvbnRhaW5lclZhcmlhbnRzIiwiaGlkZGVuIiwib3BhY2l0eSIsInZpc2libGUiLCJ0cmFuc2l0aW9uIiwic3RhZ2dlckNoaWxkcmVuIiwiaXRlbVZhcmlhbnRzIiwieSIsImR1cmF0aW9uIiwic2VjdGlvbiIsImNsYXNzTmFtZSIsImRpdiIsImluaXRpYWwiLCJ3aGlsZUluVmlldyIsInZpZXdwb3J0Iiwib25jZSIsImgyIiwic3BhbiIsInAiLCJkZWxheSIsIm1hcCIsImJ1dHRvbiIsIm9uQ2xpY2siLCJjb3VudCIsIkZpbHRlciIsImxlbmd0aCIsIkdyaWQiLCJMaXN0IiwidmFyaWFudHMiLCJQcm9kdWN0Q2FyZCIsImNvbnNvbGUiLCJsb2ciLCJ2YXJpYW50Iiwic2l6ZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Products.tsx\n"));

/***/ })

});