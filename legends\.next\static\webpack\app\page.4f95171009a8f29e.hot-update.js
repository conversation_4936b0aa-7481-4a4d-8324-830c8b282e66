"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/Products.tsx":
/*!*************************************!*\
  !*** ./src/components/Products.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Products: function() { return /* binding */ Products; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _ui_Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* __next_internal_client_entry_do_not_use__ Products auto */ \nvar _s = $RefreshSig$();\n\n\n\n// Netflix-style categories\nconst productCategories = [\n    {\n        id: \"trending\",\n        name: \"Trending Now\",\n        description: \"Hot products everyone is buying\"\n    },\n    {\n        id: \"consoles\",\n        name: \"Gaming Consoles\",\n        description: \"Latest gaming systems\"\n    },\n    {\n        id: \"popular\",\n        name: \"Popular This Week\",\n        description: \"Most loved by gamers\"\n    },\n    {\n        id: \"accessories\",\n        name: \"Gaming Accessories\",\n        description: \"Enhance your gaming setup\"\n    },\n    {\n        id: \"games\",\n        name: \"Latest Games\",\n        description: \"New releases and classics\"\n    }\n];\n// Organized by Netflix-style categories\nconst productsByCategory = {\n    trending: [\n        {\n            id: 1,\n            title: \"PlayStation 5 Console\",\n            subtitle: \"Next-Gen Gaming Experience\",\n            price: \"ETB 45,000\",\n            originalPrice: \"ETB 50,000\",\n            discount: \"10\",\n            rating: 5,\n            image: \"/images/device1.jpg\",\n            description: \"Experience lightning-fast loading with an ultra-high speed SSD, deeper immersion with support for haptic feedback, adaptive triggers and 3D Audio.\",\n            isNew: true\n        },\n        {\n            id: 2,\n            title: \"Xbox Series X\",\n            subtitle: \"The Fastest, Most Powerful Xbox Ever\",\n            price: \"ETB 42,000\",\n            originalPrice: \"ETB 47,000\",\n            discount: \"11\",\n            rating: 5,\n            image: \"/images/xbox.jpg\",\n            description: \"Experience the modernized design of the Xbox Wireless Controller, featuring sculpted surfaces and refined geometry for enhanced comfort.\",\n            isNew: true\n        },\n        {\n            id: 3,\n            title: \"FIFA 24\",\n            subtitle: \"The World's Game\",\n            price: \"ETB 3,200\",\n            originalPrice: \"ETB 3,800\",\n            discount: \"16\",\n            rating: 4,\n            image: \"/images/gl5.jpg\",\n            description: \"EA SPORTS FC 24 welcomes you to The World's Game: the most true-to-football experience ever with HyperMotionV.\",\n            isHot: true\n        },\n        {\n            id: 4,\n            title: \"Cyberpunk 2077\",\n            subtitle: \"Open-World Action Adventure\",\n            price: \"ETB 3,800\",\n            originalPrice: \"ETB 4,500\",\n            discount: \"16\",\n            rating: 4,\n            image: \"/images/gl7.jpg\",\n            description: \"An open-world, action-adventure story set in Night City, a megalopolis obsessed with power, glamour and body modification.\",\n            isNew: false\n        }\n    ],\n    consoles: [\n        {\n            id: 5,\n            title: \"PlayStation 5 Console\",\n            subtitle: \"Next-Gen Gaming\",\n            price: \"ETB 45,000\",\n            originalPrice: \"ETB 50,000\",\n            discount: \"10\",\n            rating: 5,\n            image: \"/images/device1.jpg\",\n            description: \"The PS5 console unleashes new gaming possibilities that you never anticipated.\",\n            isNew: true\n        },\n        {\n            id: 6,\n            title: \"Xbox Series X\",\n            subtitle: \"Power Your Dreams\",\n            price: \"ETB 42,000\",\n            originalPrice: \"ETB 47,000\",\n            discount: \"11\",\n            rating: 5,\n            image: \"/images/xbox.jpg\",\n            description: \"Our fastest, most powerful console ever.\",\n            isNew: true\n        },\n        {\n            id: 7,\n            title: \"Nintendo Switch OLED\",\n            subtitle: \"Handheld Gaming Revolution\",\n            price: \"ETB 28,000\",\n            originalPrice: \"ETB 32,000\",\n            discount: \"13\",\n            rating: 4,\n            image: \"/images/device3.jpg\",\n            description: \"Meet the newest member of the Nintendo Switch family with a vibrant 7-inch OLED screen.\",\n            isNew: false\n        }\n    ],\n    popular: [\n        {\n            id: 8,\n            title: \"Call of Duty: Modern Warfare III\",\n            subtitle: \"Ultimate Combat Experience\",\n            price: \"ETB 3,500\",\n            originalPrice: \"ETB 4,200\",\n            discount: \"17\",\n            rating: 5,\n            image: \"/images/\\uD83D\\uDD2BCall of duty.jpg\",\n            description: \"In the direct sequel to the record-breaking Call of Duty: Modern Warfare II.\",\n            isHot: true\n        },\n        {\n            id: 9,\n            title: \"Resident Evil 7\",\n            subtitle: \"Survival Horror Masterpiece\",\n            price: \"ETB 2,800\",\n            originalPrice: \"ETB 3,200\",\n            discount: \"13\",\n            rating: 5,\n            image: \"/images/re7.jpg\",\n            description: \"Fear and isolation seep through the walls of an abandoned southern farmhouse.\",\n            isNew: false\n        },\n        {\n            id: 10,\n            title: \"Grand Theft Auto V\",\n            subtitle: \"Open World Crime Epic\",\n            price: \"ETB 2,500\",\n            originalPrice: \"ETB 3,000\",\n            discount: \"17\",\n            rating: 5,\n            image: \"/images/gl6.jpg\",\n            description: \"When a young street hustler, a retired bank robber and a terrifying psychopath find themselves entangled.\",\n            isNew: false\n        }\n    ],\n    accessories: [\n        {\n            id: 11,\n            title: \"DualSense Wireless Controller\",\n            subtitle: \"Feel Every Moment\",\n            price: \"ETB 4,500\",\n            originalPrice: \"ETB 5,200\",\n            discount: \"13\",\n            rating: 5,\n            image: \"/images/device4.jpg\",\n            description: \"Discover a deeper gaming experience with the innovative PS5 controller.\",\n            isNew: true\n        },\n        {\n            id: 12,\n            title: \"Gaming Headset Pro\",\n            subtitle: \"Immersive Audio Experience\",\n            price: \"ETB 3,200\",\n            originalPrice: \"ETB 4,000\",\n            discount: \"20\",\n            rating: 4,\n            image: \"/images/device5.jpg\",\n            description: \"Crystal clear audio and comfortable design for extended gaming sessions.\",\n            isNew: false\n        },\n        {\n            id: 13,\n            title: 'Gaming Monitor 27\"',\n            subtitle: \"4K Gaming Display\",\n            price: \"ETB 15,000\",\n            originalPrice: \"ETB 18,000\",\n            discount: \"17\",\n            rating: 5,\n            image: \"/images/v3.jpg\",\n            description: \"Ultra-fast response time and stunning 4K resolution for competitive gaming.\",\n            isHot: true\n        }\n    ],\n    games: [\n        {\n            id: 14,\n            title: \"FIFA 24\",\n            subtitle: \"The Beautiful Game\",\n            price: \"ETB 3,200\",\n            originalPrice: \"ETB 3,800\",\n            discount: \"16\",\n            rating: 4,\n            image: \"/images/gl5.jpg\",\n            description: \"The most authentic football experience with updated teams and players.\",\n            isNew: true\n        },\n        {\n            id: 15,\n            title: \"Cyberpunk 2077\",\n            subtitle: \"Futuristic RPG\",\n            price: \"ETB 3,800\",\n            originalPrice: \"ETB 4,500\",\n            discount: \"16\",\n            rating: 4,\n            image: \"/images/gl7.jpg\",\n            description: \"An open-world, action-adventure story set in the dark future of Night City.\",\n            isNew: false\n        },\n        {\n            id: 16,\n            title: \"Resident Evil 7\",\n            subtitle: \"Horror Survival\",\n            price: \"ETB 2,800\",\n            originalPrice: \"ETB 3,200\",\n            discount: \"13\",\n            rating: 5,\n            image: \"/images/re7.jpg\",\n            description: \"A chilling tale of survival horror that will keep you on the edge of your seat.\",\n            isNew: false\n        }\n    ]\n};\nconst Products = ()=>{\n    _s();\n    const [activeCategory, setActiveCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"grid\");\n    const filteredProducts = activeCategory === \"all\" ? products : products.filter((product)=>product.category === activeCategory || product.type === activeCategory);\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.1\n            }\n        }\n    };\n    const itemVariants = {\n        hidden: {\n            opacity: 0,\n            y: 20\n        },\n        visible: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.5\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"products\",\n        className: \"py-20 bg-gray-900/50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.6\n                    },\n                    className: \"text-center mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl md:text-5xl font-bold text-white mb-4\",\n                            children: [\n                                \"Our \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"gradient-text\",\n                                    children: \"Products\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\legends\\\\legends\\\\src\\\\components\\\\Products.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\legends\\\\legends\\\\src\\\\components\\\\Products.tsx\",\n                            lineNumber: 263,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-400 max-w-2xl mx-auto\",\n                            children: \"Discover our extensive collection of gaming consoles, accessories, and the latest games\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\legends\\\\legends\\\\src\\\\components\\\\Products.tsx\",\n                            lineNumber: 266,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\legends\\\\legends\\\\src\\\\components\\\\Products.tsx\",\n                    lineNumber: 256,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.6,\n                        delay: 0.2\n                    },\n                    className: \"flex flex-wrap justify-center gap-4 mb-8\",\n                    children: productCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActiveCategory(category.id),\n                            className: \"px-6 py-3 rounded-full font-medium transition-all duration-300 \".concat(activeCategory === category.id ? \"bg-blue-600 text-white shadow-lg\" : \"bg-gray-800 text-gray-300 hover:bg-gray-700 hover:text-white\"),\n                            children: [\n                                category.name,\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-2 text-sm opacity-75\",\n                                    children: [\n                                        \"(\",\n                                        category.count,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\legends\\\\legends\\\\src\\\\components\\\\Products.tsx\",\n                                    lineNumber: 290,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, category.id, true, {\n                            fileName: \"D:\\\\legends\\\\legends\\\\src\\\\components\\\\Products.tsx\",\n                            lineNumber: 280,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"D:\\\\legends\\\\legends\\\\src\\\\components\\\\Products.tsx\",\n                    lineNumber: 272,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.6,\n                        delay: 0.3\n                    },\n                    className: \"flex items-center justify-between mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Filter, {\n                                    className: \"h-5 w-5 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\legends\\\\legends\\\\src\\\\components\\\\Products.tsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-400\",\n                                    children: [\n                                        \"Showing \",\n                                        filteredProducts.length,\n                                        \" products\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\legends\\\\legends\\\\src\\\\components\\\\Products.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\legends\\\\legends\\\\src\\\\components\\\\Products.tsx\",\n                            lineNumber: 303,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setViewMode(\"grid\"),\n                                    className: \"p-2 rounded-lg transition-colors duration-300 \".concat(viewMode === \"grid\" ? \"bg-blue-600 text-white\" : \"bg-gray-800 text-gray-400\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Grid, {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\legends\\\\legends\\\\src\\\\components\\\\Products.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\legends\\\\legends\\\\src\\\\components\\\\Products.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setViewMode(\"list\"),\n                                    className: \"p-2 rounded-lg transition-colors duration-300 \".concat(viewMode === \"list\" ? \"bg-blue-600 text-white\" : \"bg-gray-800 text-gray-400\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(List, {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\legends\\\\legends\\\\src\\\\components\\\\Products.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\legends\\\\legends\\\\src\\\\components\\\\Products.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\legends\\\\legends\\\\src\\\\components\\\\Products.tsx\",\n                            lineNumber: 310,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\legends\\\\legends\\\\src\\\\components\\\\Products.tsx\",\n                    lineNumber: 296,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    variants: containerVariants,\n                    initial: \"hidden\",\n                    whileInView: \"visible\",\n                    viewport: {\n                        once: true\n                    },\n                    className: \"grid gap-6 \".concat(viewMode === \"grid\" ? \"grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4\" : \"grid-cols-1\"),\n                    children: filteredProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            variants: itemVariants,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductCard, {\n                                title: product.title,\n                                price: product.price,\n                                originalPrice: product.originalPrice,\n                                discount: product.discount,\n                                rating: product.rating,\n                                image: product.image,\n                                category: product.category,\n                                onClick: ()=>console.log(\"Product clicked:\", product.id)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\legends\\\\legends\\\\src\\\\components\\\\Products.tsx\",\n                                lineNumber: 344,\n                                columnNumber: 15\n                            }, undefined)\n                        }, product.id, false, {\n                            fileName: \"D:\\\\legends\\\\legends\\\\src\\\\components\\\\Products.tsx\",\n                            lineNumber: 343,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"D:\\\\legends\\\\legends\\\\src\\\\components\\\\Products.tsx\",\n                    lineNumber: 331,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.6,\n                        delay: 0.4\n                    },\n                    className: \"text-center mt-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        variant: \"outline\",\n                        size: \"lg\",\n                        children: \"Load More Products\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\legends\\\\legends\\\\src\\\\components\\\\Products.tsx\",\n                        lineNumber: 366,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\legends\\\\legends\\\\src\\\\components\\\\Products.tsx\",\n                    lineNumber: 359,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\legends\\\\legends\\\\src\\\\components\\\\Products.tsx\",\n            lineNumber: 254,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\legends\\\\legends\\\\src\\\\components\\\\Products.tsx\",\n        lineNumber: 253,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Products, \"d8Uk5XcjjH/DLyQddN99upbGdyY=\");\n_c = Products;\nvar _c;\n$RefreshReg$(_c, \"Products\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Products.tsx\n"));

/***/ })

});