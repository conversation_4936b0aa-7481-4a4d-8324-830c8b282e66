import type { Metadata } from "next";
import localFont from "next/font/local";
import "./globals.css";

const geistSans = localFont({
  src: "./fonts/GeistVF.woff",
  variable: "--font-geist-sans",
  weight: "100 900",
});
const geistMono = localFont({
  src: "./fonts/GeistMonoVF.woff",
  variable: "--font-geist-mono",
  weight: "100 900",
});

export const metadata: Metadata = {
  title: "Legends Gaming Store - Game Apps & Console Store",
  description: "Your ultimate destination for game apps, gaming consoles, and top-up services. Get the best deals on PUBG UC, Free Fire Diamonds, TikTok Coins, and more!",
  keywords: "gaming, game apps, gaming console, PUBG UC, Free Fire Diamonds, TikTok Coins, gaming store, Ethiopia",
  authors: [{ name: "Legends Gaming Store" }],
  robots: "index, follow",
  openGraph: {
    title: "Legends Gaming Store - Game Apps & Console Store",
    description: "Your ultimate destination for game apps, gaming consoles, and top-up services.",
    type: "website",
    locale: "en_US",
  },
};

export const viewport = {
  width: 'device-width',
  initialScale: 1,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="dark">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased dark bg-background text-foreground`}
      >
        {children}
      </body>
    </html>
  );
}
