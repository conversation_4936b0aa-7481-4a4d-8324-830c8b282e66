'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronDown, Upload, CreditCard, Smartphone, Star } from 'lucide-react';
import { Button } from './ui/Button';
import { Card } from './ui/Card';

interface TopUpService {
  id: string;
  name: string;
  icon: string;
  description: string;
  packages: {
    amount: string;
    price: string;
    originalPrice?: string;
    discount?: string;
    popular?: boolean;
  }[];
}

const topUpServices: TopUpService[] = [
  {
    id: 'tiktok-coin',
    name: 'TikTok Coin',
    icon: '🎵',
    description: 'Buy TikTok coins to support your favorite creators and unlock premium features',
    packages: [
      { amount: '100 Coins', price: 'ETB 45', originalPrice: 'ETB 50', discount: '10' },
      { amount: '500 Coins', price: 'ETB 220', originalPrice: 'ETB 250', discount: '12', popular: true },
      { amount: '1000 Coins', price: 'ETB 430', originalPrice: 'ETB 500', discount: '14' },
      { amount: '2500 Coins', price: 'ETB 1050', originalPrice: 'ETB 1250', discount: '16' }
    ]
  },
  {
    id: 'pubg-uc',
    name: 'PUBG UC',
    icon: '🎮',
    description: 'Get PUBG Mobile UC (Unknown Cash) for skins, outfits, and battle passes',
    packages: [
      { amount: '60 UC', price: 'ETB 35', originalPrice: 'ETB 40', discount: '13' },
      { amount: '325 UC', price: 'ETB 180', originalPrice: 'ETB 200', discount: '10', popular: true },
      { amount: '660 UC', price: 'ETB 350', originalPrice: 'ETB 400', discount: '13' },
      { amount: '1800 UC', price: 'ETB 950', originalPrice: 'ETB 1100', discount: '14' }
    ]
  },
  {
    id: 'free-fire-diamond',
    name: 'Free Fire Diamond',
    icon: '💎',
    description: 'Purchase Free Fire diamonds for characters, weapons, and exclusive items',
    packages: [
      { amount: '100 Diamonds', price: 'ETB 40', originalPrice: 'ETB 45', discount: '11' },
      { amount: '310 Diamonds', price: 'ETB 120', originalPrice: 'ETB 140', discount: '14', popular: true },
      { amount: '520 Diamonds', price: 'ETB 200', originalPrice: 'ETB 230', discount: '13' },
      { amount: '1080 Diamonds', price: 'ETB 400', originalPrice: 'ETB 460', discount: '13' }
    ]
  },
  {
    id: 'free-fire-diamond-north',
    name: 'Free Fire Diamond North',
    icon: '💎',
    description: 'Free Fire diamonds for North American servers with instant delivery',
    packages: [
      { amount: '100 Diamonds', price: 'ETB 42', originalPrice: 'ETB 48', discount: '13' },
      { amount: '310 Diamonds', price: 'ETB 125', originalPrice: 'ETB 145', discount: '14', popular: true },
      { amount: '520 Diamonds', price: 'ETB 205', originalPrice: 'ETB 235', discount: '13' },
      { amount: '1080 Diamonds', price: 'ETB 410', originalPrice: 'ETB 470', discount: '13' }
    ]
  },
  {
    id: 'telegram-star',
    name: 'Telegram Star',
    icon: '⭐',
    description: 'Telegram Stars for premium features and exclusive content access',
    packages: [
      { amount: '50 Stars', price: 'ETB 25', originalPrice: 'ETB 30', discount: '17' },
      { amount: '100 Stars', price: 'ETB 48', originalPrice: 'ETB 55', discount: '13', popular: true },
      { amount: '250 Stars', price: 'ETB 115', originalPrice: 'ETB 135', discount: '15' },
      { amount: '500 Stars', price: 'ETB 220', originalPrice: 'ETB 260', discount: '15' }
    ]
  },
  {
    id: 'telegram-premium',
    name: 'Telegram Premium',
    icon: '💫',
    description: 'Telegram Premium subscription for enhanced features and capabilities',
    packages: [
      { amount: '1 Month', price: 'ETB 180', originalPrice: 'ETB 200', discount: '10' },
      { amount: '3 Months', price: 'ETB 520', originalPrice: 'ETB 600', discount: '13', popular: true },
      { amount: '6 Months', price: 'ETB 1000', originalPrice: 'ETB 1200', discount: '17' },
      { amount: '12 Months', price: 'ETB 1900', originalPrice: 'ETB 2400', discount: '21' }
    ]
  },
  {
    id: 'netflix-account',
    name: 'Netflix Account',
    icon: '🎬',
    description: 'Netflix premium accounts with HD and 4K streaming capabilities',
    packages: [
      { amount: '1 Month Basic', price: 'ETB 350', originalPrice: 'ETB 400', discount: '13' },
      { amount: '1 Month Standard', price: 'ETB 450', originalPrice: 'ETB 520', discount: '13', popular: true },
      { amount: '1 Month Premium', price: 'ETB 550', originalPrice: 'ETB 650', discount: '15' },
      { amount: '3 Months Premium', price: 'ETB 1500', originalPrice: 'ETB 1800', discount: '17' }
    ]
  },
  {
    id: 'efootball-coin',
    name: 'eFootball Coin',
    icon: '⚽',
    description: 'eFootball coins for player packs, managers, and premium content',
    packages: [
      { amount: '100 Coins', price: 'ETB 35', originalPrice: 'ETB 40', discount: '13' },
      { amount: '550 Coins', price: 'ETB 180', originalPrice: 'ETB 210', discount: '14', popular: true },
      { amount: '1200 Coins', price: 'ETB 380', originalPrice: 'ETB 450', discount: '16' },
      { amount: '2500 Coins', price: 'ETB 750', originalPrice: 'ETB 900', discount: '17' }
    ]
  },
  {
    id: 'roblox-robux',
    name: 'Roblox Robux',
    icon: '🎮',
    description: 'Roblox Robux for avatar items, game passes, and premium features',
    packages: [
      { amount: '80 Robux', price: 'ETB 35', originalPrice: 'ETB 40', discount: '13' },
      { amount: '400 Robux', price: 'ETB 160', originalPrice: 'ETB 185', discount: '14', popular: true },
      { amount: '800 Robux', price: 'ETB 310', originalPrice: 'ETB 360', discount: '14' },
      { amount: '1700 Robux', price: 'ETB 620', originalPrice: 'ETB 720', discount: '14' }
    ]
  },
  {
    id: 'blood-strike',
    name: 'Blood Strike',
    icon: '🔫',
    description: 'Blood Strike gold and premium currency for weapons and skins',
    packages: [
      { amount: '100 Gold', price: 'ETB 30', originalPrice: 'ETB 35', discount: '14' },
      { amount: '520 Gold', price: 'ETB 150', originalPrice: 'ETB 175', discount: '14', popular: true },
      { amount: '1050 Gold', price: 'ETB 290', originalPrice: 'ETB 340', discount: '15' },
      { amount: '2180 Gold', price: 'ETB 580', originalPrice: 'ETB 680', discount: '15' }
    ]
  },
  {
    id: 'delta-force',
    name: 'Delta Force',
    icon: '🪖',
    description: 'Delta Force premium currency for tactical gear and weapons',
    packages: [
      { amount: '100 Credits', price: 'ETB 40', originalPrice: 'ETB 45', discount: '11' },
      { amount: '500 Credits', price: 'ETB 190', originalPrice: 'ETB 220', discount: '14', popular: true },
      { amount: '1000 Credits', price: 'ETB 370', originalPrice: 'ETB 430', discount: '14' },
      { amount: '2500 Credits', price: 'ETB 900', originalPrice: 'ETB 1050', discount: '14' }
    ]
  },
  {
    id: 'call-of-duty',
    name: 'Call of Duty',
    icon: '🎯',
    description: 'COD Points for Call of Duty Mobile and Warzone',
    packages: [
      { amount: '80 CP', price: 'ETB 35', originalPrice: 'ETB 40', discount: '13' },
      { amount: '400 CP', price: 'ETB 170', originalPrice: 'ETB 195', discount: '13', popular: true },
      { amount: '1000 CP', price: 'ETB 420', originalPrice: 'ETB 485', discount: '13' },
      { amount: '2400 CP', price: 'ETB 980', originalPrice: 'ETB 1140', discount: '14' }
    ]
  },
  {
    id: 'fortnite-vbucks',
    name: 'Fortnite V-Bucks',
    icon: '🏗️',
    description: 'Fortnite V-Bucks for skins, emotes, and battle passes',
    packages: [
      { amount: '1000 V-Bucks', price: 'ETB 380', originalPrice: 'ETB 430', discount: '12' },
      { amount: '2800 V-Bucks', price: 'ETB 1050', originalPrice: 'ETB 1200', discount: '13', popular: true },
      { amount: '5000 V-Bucks', price: 'ETB 1850', originalPrice: 'ETB 2150', discount: '14' },
      { amount: '13500 V-Bucks', price: 'ETB 4800', originalPrice: 'ETB 5600', discount: '14' }
    ]
  }
];

const paymentMethods = [
  { id: 'ebirr', name: 'eBirr', number: '0915639685', icon: '📱' },
  { id: 'cbe', name: 'CBE', number: '100023265659', icon: '🏦' },
  { id: 'mpesa', name: 'M-Pesa', number: '070635241', icon: '💳' },
  { id: 'telebirr', name: 'TeleBirr', number: '0915639685', icon: '📞' }
];

export const TopUp: React.FC = () => {
  const [activeService, setActiveService] = useState<string | null>(null);
  const [selectedPackage, setSelectedPackage] = useState<any>(null);
  const [selectedPayment, setSelectedPayment] = useState<string>('');
  const [formData, setFormData] = useState({
    email: '',
    phone: '',
    screenshot: null as File | null
  });

  const toggleService = (serviceId: string) => {
    setActiveService(activeService === serviceId ? null : serviceId);
    setSelectedPackage(null);
    setSelectedPayment('');
  };

  const handlePackageSelect = (pkg: any) => {
    setSelectedPackage(pkg);
  };

  const handlePaymentSelect = (paymentId: string) => {
    setSelectedPayment(paymentId);
  };

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setFormData({ ...formData, screenshot: e.target.files[0] });
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Order submitted:', {
      service: activeService,
      package: selectedPackage,
      payment: selectedPayment,
      formData
    });
    // Handle order submission
  };

  return (
    <section id="topup" className="py-20 bg-gray-800/30">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Top-up <span className="gradient-text">Services</span>
          </h2>
          <p className="text-xl text-gray-400 max-w-2xl mx-auto">
            Instant top-up for your favorite games and services with secure Ethiopian payment methods
          </p>
        </motion.div>

        {/* Services Accordion */}
        <div className="max-w-4xl mx-auto space-y-4">
          {topUpServices.map((service, index) => (
            <motion.div
              key={service.id}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
            >
              <Card className="overflow-hidden">
                <button
                  onClick={() => toggleService(service.id)}
                  className="w-full flex items-center justify-between p-6 text-left hover:bg-gray-800/50 transition-colors duration-300"
                >
                  <div className="flex items-center space-x-4">
                    <span className="text-3xl">{service.icon}</span>
                    <div>
                      <h3 className="text-xl font-semibold text-white">{service.name}</h3>
                      <p className="text-gray-400 text-sm">{service.description}</p>
                    </div>
                  </div>
                  <ChevronDown
                    className={`h-6 w-6 text-gray-400 transition-transform duration-300 ${
                      activeService === service.id ? 'rotate-180' : ''
                    }`}
                  />
                </button>

                <AnimatePresence>
                  {activeService === service.id && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: 'auto', opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.3 }}
                      className="border-t border-gray-700"
                    >
                      <div className="p-6">
                        {/* Package Selection */}
                        <div className="mb-6">
                          <h4 className="text-lg font-semibold text-white mb-4">Select Package</h4>
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                            {service.packages.map((pkg, pkgIndex) => (
                              <motion.button
                                key={pkgIndex}
                                whileHover={{ scale: 1.02 }}
                                whileTap={{ scale: 0.98 }}
                                onClick={() => handlePackageSelect(pkg)}
                                className={`relative p-4 rounded-lg border-2 transition-all duration-300 ${
                                  selectedPackage === pkg
                                    ? 'border-blue-500 bg-blue-500/10'
                                    : 'border-gray-600 hover:border-gray-500'
                                }`}
                              >
                                {pkg.popular && (
                                  <div className="absolute -top-2 left-1/2 transform -translate-x-1/2">
                                    <span className="bg-blue-600 text-white text-xs px-2 py-1 rounded-full flex items-center">
                                      <Star className="h-3 w-3 mr-1" />
                                      Popular
                                    </span>
                                  </div>
                                )}
                                <div className="text-center">
                                  <div className="text-white font-semibold">{pkg.amount}</div>
                                  <div className="text-blue-400 font-bold text-lg">{pkg.price}</div>
                                  {pkg.originalPrice && (
                                    <div className="text-gray-500 text-sm line-through">{pkg.originalPrice}</div>
                                  )}
                                  {pkg.discount && (
                                    <div className="text-green-400 text-sm">Save {pkg.discount}%</div>
                                  )}
                                </div>
                              </motion.button>
                            ))}
                          </div>
                        </div>

                        {selectedPackage && (
                          <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            className="space-y-6"
                          >
                            {/* Contact Information */}
                            <div>
                              <h4 className="text-lg font-semibold text-white mb-4">Contact Information</h4>
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <input
                                  type="email"
                                  placeholder="Email Address"
                                  value={formData.email}
                                  onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                                  className="w-full p-3 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none"
                                />
                                <input
                                  type="tel"
                                  placeholder="Phone Number"
                                  value={formData.phone}
                                  onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                                  className="w-full p-3 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none"
                                />
                              </div>
                            </div>

                            {/* Payment Method Selection */}
                            <div>
                              <h4 className="text-lg font-semibold text-white mb-4">Payment Method</h4>
                              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                                {paymentMethods.map((method) => (
                                  <button
                                    key={method.id}
                                    onClick={() => handlePaymentSelect(method.id)}
                                    className={`p-4 rounded-lg border-2 transition-all duration-300 ${
                                      selectedPayment === method.id
                                        ? 'border-blue-500 bg-blue-500/10'
                                        : 'border-gray-600 hover:border-gray-500'
                                    }`}
                                  >
                                    <div className="text-center">
                                      <div className="text-2xl mb-2">{method.icon}</div>
                                      <div className="text-white font-semibold">{method.name}</div>
                                      <div className="text-gray-400 text-sm">{method.number}</div>
                                    </div>
                                  </button>
                                ))}
                              </div>
                            </div>

                            {/* Screenshot Upload */}
                            {selectedPayment && (
                              <motion.div
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                              >
                                <h4 className="text-lg font-semibold text-white mb-4">Upload Payment Screenshot</h4>
                                <div className="border-2 border-dashed border-gray-600 rounded-lg p-6 text-center">
                                  <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                                  <p className="text-gray-400 mb-4">
                                    Upload screenshot of your payment to{' '}
                                    <span className="text-blue-400 font-semibold">
                                      {paymentMethods.find(m => m.id === selectedPayment)?.number}
                                    </span>
                                  </p>
                                  <input
                                    type="file"
                                    accept="image/*"
                                    onChange={handleFileUpload}
                                    className="hidden"
                                    id="screenshot-upload"
                                  />
                                  <label htmlFor="screenshot-upload">
                                    <Button variant="outline" className="cursor-pointer">
                                      Choose File
                                    </Button>
                                  </label>
                                  {formData.screenshot && (
                                    <p className="text-green-400 mt-2">
                                      File selected: {formData.screenshot.name}
                                    </p>
                                  )}
                                </div>
                              </motion.div>
                            )}

                            {/* Submit Button */}
                            {selectedPayment && formData.email && formData.phone && (
                              <motion.div
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                className="text-center"
                              >
                                <Button
                                  variant="primary"
                                  size="lg"
                                  onClick={handleSubmit}
                                  className="w-full md:w-auto"
                                >
                                  Complete Order - {selectedPackage.price}
                                </Button>
                              </motion.div>
                            )}
                          </motion.div>
                        )}
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};
