{"name": "legends", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"framer-motion": "^12.15.0", "lucide-react": "^0.511.0", "next": "14.2.29", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.56.4"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.29", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}