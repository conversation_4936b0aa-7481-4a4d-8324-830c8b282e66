'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Filter, Grid, List, Star, Heart } from 'lucide-react';
import { ProductCard } from './ui/Card';
import { Button } from './ui/Button';

const productCategories = [
  { id: 'all', name: 'All Products', count: 24 },
  { id: 'trending', name: 'Trending', count: 8 },
  { id: 'popular', name: 'Popular', count: 12 },
  { id: 'consoles', name: 'Consoles', count: 6 },
  { id: 'accessories', name: 'Accessories', count: 15 },
  { id: 'games', name: 'Games', count: 20 }
];

const products = [
  // Trending Products
  {
    id: 1,
    title: "PlayStation 5 Console",
    price: "ETB 45,000",
    originalPrice: "ETB 50,000",
    discount: "10",
    rating: 5,
    image: "https://images.unsplash.com/photo-1606144042614-b2417e99c4e3?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
    category: "trending",
    type: "consoles"
  },
  {
    id: 2,
    title: "Xbox Series X",
    price: "ETB 42,000",
    originalPrice: "ETB 47,000",
    discount: "11",
    rating: 5,
    image: "https://images.unsplash.com/photo-1621259182978-fbf93132d53d?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
    category: "trending",
    type: "consoles"
  },
  {
    id: 3,
    title: "Nintendo Switch OLED",
    price: "ETB 28,000",
    originalPrice: "ETB 32,000",
    discount: "13",
    rating: 4,
    image: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
    category: "trending",
    type: "consoles"
  },
  
  // Popular Products
  {
    id: 4,
    title: "DualSense Wireless Controller",
    price: "ETB 4,500",
    originalPrice: "ETB 5,200",
    discount: "13",
    rating: 5,
    image: "https://images.unsplash.com/photo-1592840062661-2c9e8b8b8b8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
    category: "popular",
    type: "accessories"
  },
  {
    id: 5,
    title: "Gaming Headset Pro",
    price: "ETB 3,200",
    originalPrice: "ETB 4,000",
    discount: "20",
    rating: 4,
    image: "https://images.unsplash.com/photo-1599669454699-248893623440?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
    category: "popular",
    type: "accessories"
  },
  {
    id: 6,
    title: "Mechanical Gaming Keyboard",
    price: "ETB 2,800",
    originalPrice: "ETB 3,500",
    discount: "20",
    rating: 4,
    image: "https://images.unsplash.com/photo-1541140532154-b024d705b90a?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
    category: "popular",
    type: "accessories"
  },
  
  // Games
  {
    id: 7,
    title: "Call of Duty: Modern Warfare III",
    price: "ETB 3,500",
    originalPrice: "ETB 4,200",
    discount: "17",
    rating: 5,
    image: "https://images.unsplash.com/photo-1552820728-8b83bb6b773f?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
    category: "popular",
    type: "games"
  },
  {
    id: 8,
    title: "FIFA 24",
    price: "ETB 3,200",
    originalPrice: "ETB 3,800",
    discount: "16",
    rating: 4,
    image: "https://images.unsplash.com/photo-1574680096145-d05b474e2155?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
    category: "trending",
    type: "games"
  }
];

export const Products: React.FC = () => {
  const [activeCategory, setActiveCategory] = useState('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  const filteredProducts = activeCategory === 'all' 
    ? products 
    : products.filter(product => product.category === activeCategory || product.type === activeCategory);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5
      }
    }
  };

  return (
    <section id="products" className="py-20 bg-gray-900/50">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Our <span className="gradient-text">Products</span>
          </h2>
          <p className="text-xl text-gray-400 max-w-2xl mx-auto">
            Discover our extensive collection of gaming consoles, accessories, and the latest games
          </p>
        </motion.div>

        {/* Category Filter */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="flex flex-wrap justify-center gap-4 mb-8"
        >
          {productCategories.map((category) => (
            <button
              key={category.id}
              onClick={() => setActiveCategory(category.id)}
              className={`px-6 py-3 rounded-full font-medium transition-all duration-300 ${
                activeCategory === category.id
                  ? 'bg-blue-600 text-white shadow-lg'
                  : 'bg-gray-800 text-gray-300 hover:bg-gray-700 hover:text-white'
              }`}
            >
              {category.name}
              <span className="ml-2 text-sm opacity-75">({category.count})</span>
            </button>
          ))}
        </motion.div>

        {/* View Controls */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="flex items-center justify-between mb-8"
        >
          <div className="flex items-center space-x-4">
            <Filter className="h-5 w-5 text-gray-400" />
            <span className="text-gray-400">
              Showing {filteredProducts.length} products
            </span>
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 rounded-lg transition-colors duration-300 ${
                viewMode === 'grid' ? 'bg-blue-600 text-white' : 'bg-gray-800 text-gray-400'
              }`}
            >
              <Grid className="h-5 w-5" />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 rounded-lg transition-colors duration-300 ${
                viewMode === 'list' ? 'bg-blue-600 text-white' : 'bg-gray-800 text-gray-400'
              }`}
            >
              <List className="h-5 w-5" />
            </button>
          </div>
        </motion.div>

        {/* Products Grid */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className={`grid gap-6 ${
            viewMode === 'grid' 
              ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' 
              : 'grid-cols-1'
          }`}
        >
          {filteredProducts.map((product) => (
            <motion.div key={product.id} variants={itemVariants}>
              <ProductCard
                title={product.title}
                price={product.price}
                originalPrice={product.originalPrice}
                discount={product.discount}
                rating={product.rating}
                image={product.image}
                category={product.category}
                onClick={() => console.log('Product clicked:', product.id)}
              />
            </motion.div>
          ))}
        </motion.div>

        {/* Load More Button */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="text-center mt-12"
        >
          <Button variant="outline" size="lg">
            Load More Products
          </Button>
        </motion.div>
      </div>
    </section>
  );
};
