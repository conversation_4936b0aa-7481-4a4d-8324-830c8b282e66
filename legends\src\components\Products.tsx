'use client';

import React, { useState, useRef } from 'react';
import { motion } from 'framer-motion';
import { ChevronLeft, ChevronRight, Star, Play, Info, Plus } from 'lucide-react';
import { Button } from './ui/Button';

// Netflix-style categories
const productCategories = [
  { id: 'trending', name: 'Trending Now', description: 'Hot products everyone is buying' },
  { id: 'consoles', name: 'Gaming Consoles', description: 'Latest gaming systems' },
  { id: 'popular', name: 'Popular This Week', description: 'Most loved by gamers' },
  { id: 'accessories', name: 'Gaming Accessories', description: 'Enhance your gaming setup' },
  { id: 'games', name: 'Latest Games', description: 'New releases and classics' }
];

// Organized by Netflix-style categories
const productsByCategory = {
  trending: [
    {
      id: 1,
      title: "PlayStation 5 Console",
      subtitle: "Next-Gen Gaming Experience",
      price: "ETB 45,000",
      originalPrice: "ETB 50,000",
      discount: "10",
      rating: 5,
      image: "/images/device1.jpg",
      description: "Experience lightning-fast loading with an ultra-high speed SSD, deeper immersion with support for haptic feedback, adaptive triggers and 3D Audio.",
      isNew: true
    },
    {
      id: 2,
      title: "Xbox Series X",
      subtitle: "The Fastest, Most Powerful Xbox Ever",
      price: "ETB 42,000",
      originalPrice: "ETB 47,000",
      discount: "11",
      rating: 5,
      image: "/images/xbox.jpg",
      description: "Experience the modernized design of the Xbox Wireless Controller, featuring sculpted surfaces and refined geometry for enhanced comfort.",
      isNew: true
    },
    {
      id: 3,
      title: "FIFA 24",
      subtitle: "The World's Game",
      price: "ETB 3,200",
      originalPrice: "ETB 3,800",
      discount: "16",
      rating: 4,
      image: "/images/gl5.jpg",
      description: "EA SPORTS FC 24 welcomes you to The World's Game: the most true-to-football experience ever with HyperMotionV.",
      isHot: true
    },
    {
      id: 4,
      title: "Cyberpunk 2077",
      subtitle: "Open-World Action Adventure",
      price: "ETB 3,800",
      originalPrice: "ETB 4,500",
      discount: "16",
      rating: 4,
      image: "/images/gl7.jpg",
      description: "An open-world, action-adventure story set in Night City, a megalopolis obsessed with power, glamour and body modification.",
      isNew: false
    }
  ],
  consoles: [
    {
      id: 5,
      title: "PlayStation 5 Console",
      subtitle: "Next-Gen Gaming",
      price: "ETB 45,000",
      originalPrice: "ETB 50,000",
      discount: "10",
      rating: 5,
      image: "/images/device1.jpg",
      description: "The PS5 console unleashes new gaming possibilities that you never anticipated.",
      isNew: true
    },
    {
      id: 6,
      title: "Xbox Series X",
      subtitle: "Power Your Dreams",
      price: "ETB 42,000",
      originalPrice: "ETB 47,000",
      discount: "11",
      rating: 5,
      image: "/images/xbox.jpg",
      description: "Our fastest, most powerful console ever.",
      isNew: true
    },
    {
      id: 7,
      title: "Nintendo Switch OLED",
      subtitle: "Handheld Gaming Revolution",
      price: "ETB 28,000",
      originalPrice: "ETB 32,000",
      discount: "13",
      rating: 4,
      image: "/images/device3.jpg",
      description: "Meet the newest member of the Nintendo Switch family with a vibrant 7-inch OLED screen.",
      isNew: false
    }
  ],
  popular: [
    {
      id: 8,
      title: "Call of Duty: Modern Warfare III",
      subtitle: "Ultimate Combat Experience",
      price: "ETB 3,500",
      originalPrice: "ETB 4,200",
      discount: "17",
      rating: 5,
      image: "/images/🔫Call of duty.jpg",
      description: "In the direct sequel to the record-breaking Call of Duty: Modern Warfare II.",
      isHot: true
    },
    {
      id: 9,
      title: "Resident Evil 7",
      subtitle: "Survival Horror Masterpiece",
      price: "ETB 2,800",
      originalPrice: "ETB 3,200",
      discount: "13",
      rating: 5,
      image: "/images/re7.jpg",
      description: "Fear and isolation seep through the walls of an abandoned southern farmhouse.",
      isNew: false
    },
    {
      id: 10,
      title: "Grand Theft Auto V",
      subtitle: "Open World Crime Epic",
      price: "ETB 2,500",
      originalPrice: "ETB 3,000",
      discount: "17",
      rating: 5,
      image: "/images/gl6.jpg",
      description: "When a young street hustler, a retired bank robber and a terrifying psychopath find themselves entangled.",
      isNew: false
    }
  ],
  accessories: [
    {
      id: 11,
      title: "DualSense Wireless Controller",
      subtitle: "Feel Every Moment",
      price: "ETB 4,500",
      originalPrice: "ETB 5,200",
      discount: "13",
      rating: 5,
      image: "/images/device4.jpg",
      description: "Discover a deeper gaming experience with the innovative PS5 controller.",
      isNew: true
    },
    {
      id: 12,
      title: "Gaming Headset Pro",
      subtitle: "Immersive Audio Experience",
      price: "ETB 3,200",
      originalPrice: "ETB 4,000",
      discount: "20",
      rating: 4,
      image: "/images/device5.jpg",
      description: "Crystal clear audio and comfortable design for extended gaming sessions.",
      isNew: false
    },
    {
      id: 13,
      title: "Gaming Monitor 27\"",
      subtitle: "4K Gaming Display",
      price: "ETB 15,000",
      originalPrice: "ETB 18,000",
      discount: "17",
      rating: 5,
      image: "/images/v3.jpg",
      description: "Ultra-fast response time and stunning 4K resolution for competitive gaming.",
      isHot: true
    }
  ],
  games: [
    {
      id: 14,
      title: "FIFA 24",
      subtitle: "The Beautiful Game",
      price: "ETB 3,200",
      originalPrice: "ETB 3,800",
      discount: "16",
      rating: 4,
      image: "/images/gl5.jpg",
      description: "The most authentic football experience with updated teams and players.",
      isNew: true
    },
    {
      id: 15,
      title: "Cyberpunk 2077",
      subtitle: "Futuristic RPG",
      price: "ETB 3,800",
      originalPrice: "ETB 4,500",
      discount: "16",
      rating: 4,
      image: "/images/gl7.jpg",
      description: "An open-world, action-adventure story set in the dark future of Night City.",
      isNew: false
    },
    {
      id: 16,
      title: "Resident Evil 7",
      subtitle: "Horror Survival",
      price: "ETB 2,800",
      originalPrice: "ETB 3,200",
      discount: "13",
      rating: 5,
      image: "/images/re7.jpg",
      description: "A chilling tale of survival horror that will keep you on the edge of your seat.",
      isNew: false
    }
  ]
};

export const Products: React.FC = () => {
  const [activeCategory, setActiveCategory] = useState('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  const filteredProducts = activeCategory === 'all'
    ? products
    : products.filter(product => product.category === activeCategory || product.type === activeCategory);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5
      }
    }
  };

  return (
    <section id="products" className="py-20 bg-gray-900/50">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Our <span className="gradient-text">Products</span>
          </h2>
          <p className="text-xl text-gray-400 max-w-2xl mx-auto">
            Discover our extensive collection of gaming consoles, accessories, and the latest games
          </p>
        </motion.div>

        {/* Category Filter */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="flex flex-wrap justify-center gap-4 mb-8"
        >
          {productCategories.map((category) => (
            <button
              key={category.id}
              onClick={() => setActiveCategory(category.id)}
              className={`px-6 py-3 rounded-full font-medium transition-all duration-300 ${
                activeCategory === category.id
                  ? 'bg-blue-600 text-white shadow-lg'
                  : 'bg-gray-800 text-gray-300 hover:bg-gray-700 hover:text-white'
              }`}
            >
              {category.name}
              <span className="ml-2 text-sm opacity-75">({category.count})</span>
            </button>
          ))}
        </motion.div>

        {/* View Controls */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="flex items-center justify-between mb-8"
        >
          <div className="flex items-center space-x-4">
            <Filter className="h-5 w-5 text-gray-400" />
            <span className="text-gray-400">
              Showing {filteredProducts.length} products
            </span>
          </div>

          <div className="flex items-center space-x-2">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 rounded-lg transition-colors duration-300 ${
                viewMode === 'grid' ? 'bg-blue-600 text-white' : 'bg-gray-800 text-gray-400'
              }`}
            >
              <Grid className="h-5 w-5" />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 rounded-lg transition-colors duration-300 ${
                viewMode === 'list' ? 'bg-blue-600 text-white' : 'bg-gray-800 text-gray-400'
              }`}
            >
              <List className="h-5 w-5" />
            </button>
          </div>
        </motion.div>

        {/* Products Grid */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className={`grid gap-6 ${
            viewMode === 'grid'
              ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
              : 'grid-cols-1'
          }`}
        >
          {filteredProducts.map((product) => (
            <motion.div key={product.id} variants={itemVariants}>
              <ProductCard
                title={product.title}
                price={product.price}
                originalPrice={product.originalPrice}
                discount={product.discount}
                rating={product.rating}
                image={product.image}
                category={product.category}
                onClick={() => console.log('Product clicked:', product.id)}
              />
            </motion.div>
          ))}
        </motion.div>

        {/* Load More Button */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="text-center mt-12"
        >
          <Button variant="outline" size="lg">
            Load More Products
          </Button>
        </motion.div>
      </div>
    </section>
  );
};
