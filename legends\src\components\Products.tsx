'use client';

import React, { useState, useRef } from 'react';
import { motion } from 'framer-motion';
import { ChevronLeft, ChevronRight, Star, Play, Info, Plus } from 'lucide-react';
import { Button } from './ui/Button';

// Netflix-style categories
const productCategories = [
  { id: 'trending', name: 'Trending Now', description: 'Hot products everyone is buying' },
  { id: 'consoles', name: 'Gaming Consoles', description: 'Latest gaming systems' },
  { id: 'popular', name: 'Popular This Week', description: 'Most loved by gamers' },
  { id: 'accessories', name: 'Gaming Accessories', description: 'Enhance your gaming setup' },
  { id: 'games', name: 'Latest Games', description: 'New releases and classics' }
];

// Organized by Netflix-style categories
const productsByCategory = {
  trending: [
    {
      id: 1,
      title: "PlayStation 5 Console",
      subtitle: "Next-Gen Gaming Experience",
      price: "ETB 45,000",
      originalPrice: "ETB 50,000",
      discount: "10",
      rating: 5,
      image: "/images/device1.jpg",
      description: "Experience lightning-fast loading with an ultra-high speed SSD, deeper immersion with support for haptic feedback, adaptive triggers and 3D Audio.",
      isNew: true,
      badge: "HOT"
    },
    {
      id: 2,
      title: "Xbox Series X",
      subtitle: "The Fastest, Most Powerful Xbox Ever",
      price: "ETB 42,000",
      originalPrice: "ETB 47,000",
      discount: "11",
      rating: 5,
      image: "/images/xbox.jpg",
      description: "Experience the modernized design of the Xbox Wireless Controller, featuring sculpted surfaces and refined geometry for enhanced comfort.",
      isNew: true,
      badge: "NEW"
    },
    {
      id: 3,
      title: "FIFA 24",
      subtitle: "The World's Game",
      price: "ETB 3,200",
      originalPrice: "ETB 3,800",
      discount: "16",
      rating: 4,
      image: "/images/gl5.jpg",
      description: "EA SPORTS FC 24 welcomes you to The World's Game: the most true-to-football experience ever with HyperMotionV.",
      isHot: true,
      badge: "TRENDING"
    },
    {
      id: 4,
      title: "Cyberpunk 2077",
      subtitle: "Open-World Action Adventure",
      price: "ETB 3,800",
      originalPrice: "ETB 4,500",
      discount: "16",
      rating: 4,
      image: "/images/gl7.jpg",
      description: "An open-world, action-adventure story set in Night City, a megalopolis obsessed with power, glamour and body modification.",
      isNew: false,
      badge: "POPULAR"
    }
  ],
  consoles: [
    {
      id: 5,
      title: "PlayStation 5 Console",
      subtitle: "Next-Gen Gaming",
      price: "ETB 45,000",
      originalPrice: "ETB 50,000",
      discount: "10",
      rating: 5,
      image: "/images/device1.jpg",
      description: "The PS5 console unleashes new gaming possibilities that you never anticipated.",
      isNew: true,
      badge: "BESTSELLER"
    },
    {
      id: 6,
      title: "Xbox Series X",
      subtitle: "Power Your Dreams",
      price: "ETB 42,000",
      originalPrice: "ETB 47,000",
      discount: "11",
      rating: 5,
      image: "/images/xbox.jpg",
      description: "Our fastest, most powerful console ever.",
      isNew: true,
      badge: "NEW"
    },
    {
      id: 7,
      title: "Nintendo Switch OLED",
      subtitle: "Handheld Gaming Revolution",
      price: "ETB 28,000",
      originalPrice: "ETB 32,000",
      discount: "13",
      rating: 4,
      image: "/images/device3.jpg",
      description: "Meet the newest member of the Nintendo Switch family with a vibrant 7-inch OLED screen.",
      isNew: false,
      badge: "POPULAR"
    }
  ],
  popular: [
    {
      id: 8,
      title: "Call of Duty: Modern Warfare III",
      subtitle: "Ultimate Combat Experience",
      price: "ETB 3,500",
      originalPrice: "ETB 4,200",
      discount: "17",
      rating: 5,
      image: "/images/🔫Call of duty.jpg",
      description: "In the direct sequel to the record-breaking Call of Duty: Modern Warfare II.",
      isHot: true,
      badge: "TOP 10"
    },
    {
      id: 9,
      title: "Resident Evil 7",
      subtitle: "Survival Horror Masterpiece",
      price: "ETB 2,800",
      originalPrice: "ETB 3,200",
      discount: "13",
      rating: 5,
      image: "/images/re7.jpg",
      description: "Fear and isolation seep through the walls of an abandoned southern farmhouse.",
      isNew: false
    },
    {
      id: 10,
      title: "Grand Theft Auto V",
      subtitle: "Open World Crime Epic",
      price: "ETB 2,500",
      originalPrice: "ETB 3,000",
      discount: "17",
      rating: 5,
      image: "/images/gl6.jpg",
      description: "When a young street hustler, a retired bank robber and a terrifying psychopath find themselves entangled.",
      isNew: false
    }
  ],
  accessories: [
    {
      id: 11,
      title: "DualSense Wireless Controller",
      subtitle: "Feel Every Moment",
      price: "ETB 4,500",
      originalPrice: "ETB 5,200",
      discount: "13",
      rating: 5,
      image: "/images/device4.jpg",
      description: "Discover a deeper gaming experience with the innovative PS5 controller.",
      isNew: true
    },
    {
      id: 12,
      title: "Gaming Headset Pro",
      subtitle: "Immersive Audio Experience",
      price: "ETB 3,200",
      originalPrice: "ETB 4,000",
      discount: "20",
      rating: 4,
      image: "/images/device5.jpg",
      description: "Crystal clear audio and comfortable design for extended gaming sessions.",
      isNew: false
    },
    {
      id: 13,
      title: "Gaming Monitor 27\"",
      subtitle: "4K Gaming Display",
      price: "ETB 15,000",
      originalPrice: "ETB 18,000",
      discount: "17",
      rating: 5,
      image: "/images/v3.jpg",
      description: "Ultra-fast response time and stunning 4K resolution for competitive gaming.",
      isHot: true
    }
  ],
  games: [
    {
      id: 14,
      title: "FIFA 24",
      subtitle: "The Beautiful Game",
      price: "ETB 3,200",
      originalPrice: "ETB 3,800",
      discount: "16",
      rating: 4,
      image: "/images/gl5.jpg",
      description: "The most authentic football experience with updated teams and players.",
      isNew: true
    },
    {
      id: 15,
      title: "Cyberpunk 2077",
      subtitle: "Futuristic RPG",
      price: "ETB 3,800",
      originalPrice: "ETB 4,500",
      discount: "16",
      rating: 4,
      image: "/images/gl7.jpg",
      description: "An open-world, action-adventure story set in the dark future of Night City.",
      isNew: false
    },
    {
      id: 16,
      title: "Resident Evil 7",
      subtitle: "Horror Survival",
      price: "ETB 2,800",
      originalPrice: "ETB 3,200",
      discount: "13",
      rating: 5,
      image: "/images/re7.jpg",
      description: "A chilling tale of survival horror that will keep you on the edge of your seat.",
      isNew: false
    }
  ]
};

// Netflix-style Product Card Component
const NetflixProductCard: React.FC<{
  product: any;
  index: number;
}> = ({ product, index }) => {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      className="relative group cursor-pointer flex-shrink-0"
      style={{ width: '280px', height: '400px' }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="relative w-full h-full rounded-lg overflow-hidden bg-gray-900">
        {/* Product Image */}
        <img
          src={product.image}
          alt={product.title}
          className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
        />

        {/* Gradient Overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent" />

        {/* Badge */}
        {product.badge && (
          <div className="absolute top-3 left-3 bg-red-600 text-white text-xs font-bold px-2 py-1 rounded">
            {product.badge}
          </div>
        )}

        {/* Discount Badge */}
        {product.discount && (
          <div className="absolute top-3 right-3 bg-green-600 text-white text-xs font-bold px-2 py-1 rounded">
            -{product.discount}%
          </div>
        )}

        {/* Content */}
        <div className="absolute bottom-0 left-0 right-0 p-4">
          <h3 className="text-white font-bold text-lg mb-1 line-clamp-1">
            {product.title}
          </h3>
          <p className="text-gray-300 text-sm mb-2 line-clamp-1">
            {product.subtitle}
          </p>

          {/* Rating */}
          <div className="flex items-center mb-2">
            {[...Array(5)].map((_, i) => (
              <Star
                key={i}
                className={`h-3 w-3 ${
                  i < product.rating ? 'text-yellow-400 fill-current' : 'text-gray-600'
                }`}
              />
            ))}
            <span className="text-gray-400 text-xs ml-2">({product.rating}/5)</span>
          </div>

          {/* Price */}
          <div className="flex items-center justify-between">
            <div>
              <span className="text-blue-400 font-bold text-lg">{product.price}</span>
              {product.originalPrice && (
                <span className="text-gray-500 text-sm line-through ml-2">
                  {product.originalPrice}
                </span>
              )}
            </div>
          </div>
        </div>

        {/* Hover Overlay */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: isHovered ? 1 : 0 }}
          transition={{ duration: 0.3 }}
          className="absolute inset-0 bg-black/60 flex items-center justify-center"
        >
          <div className="text-center">
            <div className="flex space-x-3 mb-4">
              <Button variant="primary" size="sm" className="flex items-center">
                <Play className="h-4 w-4 mr-1" />
                Buy Now
              </Button>
              <Button variant="outline" size="sm" className="flex items-center">
                <Info className="h-4 w-4 mr-1" />
                Details
              </Button>
            </div>
            <p className="text-gray-300 text-sm px-4 line-clamp-3">
              {product.description}
            </p>
          </div>
        </motion.div>
      </div>
    </motion.div>
  );
};

// Netflix-style Row Component
const NetflixRow: React.FC<{
  title: string;
  description: string;
  products: any[];
}> = ({ title, description, products }) => {
  const scrollRef = useRef<HTMLDivElement>(null);

  const scroll = (direction: 'left' | 'right') => {
    if (scrollRef.current) {
      const scrollAmount = 300;
      scrollRef.current.scrollBy({
        left: direction === 'left' ? -scrollAmount : scrollAmount,
        behavior: 'smooth'
      });
    }
  };

  return (
    <div className="mb-12">
      {/* Row Header */}
      <div className="mb-4">
        <h2 className="text-2xl font-bold text-white mb-1">{title}</h2>
        <p className="text-gray-400 text-sm">{description}</p>
      </div>

      {/* Scrollable Row */}
      <div className="relative group">
        {/* Left Arrow */}
        <button
          onClick={() => scroll('left')}
          className="absolute left-0 top-1/2 transform -translate-y-1/2 z-10 bg-black/50 hover:bg-black/80 text-white p-2 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"
        >
          <ChevronLeft className="h-6 w-6" />
        </button>

        {/* Right Arrow */}
        <button
          onClick={() => scroll('right')}
          className="absolute right-0 top-1/2 transform -translate-y-1/2 z-10 bg-black/50 hover:bg-black/80 text-white p-2 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"
        >
          <ChevronRight className="h-6 w-6" />
        </button>

        {/* Products Container */}
        <div
          ref={scrollRef}
          className="flex space-x-4 overflow-x-auto scrollbar-hide pb-4"
          style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
        >
          {products.map((product, index) => (
            <NetflixProductCard key={product.id} product={product} index={index} />
          ))}
        </div>
      </div>
    </div>
  );
};

export const Products: React.FC = () => {
  return (
    <section id="products" className="py-20 bg-black">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="mb-16"
        >
          <h1 className="text-5xl md:text-6xl font-bold text-white mb-4">
            Gaming <span className="gradient-text">Store</span>
          </h1>
          <p className="text-xl text-gray-400 max-w-3xl">
            Discover thousands of games, consoles, and accessories. From the latest releases to timeless classics.
          </p>
        </motion.div>

        {/* Netflix-style Rows */}
        <div className="space-y-16">
          {productCategories.map((category, index) => (
            <motion.div
              key={category.id}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
            >
              <NetflixRow
                title={category.name}
                description={category.description}
                products={productsByCategory[category.id as keyof typeof productsByCategory] || []}
              />
            </motion.div>
          ))}
        </div>

        {/* Featured Banner */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="mt-20 relative rounded-2xl overflow-hidden"
        >
          <div className="relative h-80 bg-gradient-to-r from-blue-900 via-purple-900 to-pink-900">
            <div className="absolute inset-0 bg-black/40" />
            <div className="relative z-10 h-full flex items-center justify-center text-center px-8">
              <div>
                <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
                  Can't Find What You're Looking For?
                </h2>
                <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
                  Browse our complete catalog or contact us for special requests and custom gaming setups.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button variant="primary" size="lg" className="flex items-center">
                    <Play className="h-5 w-5 mr-2" />
                    Browse All Products
                  </Button>
                  <Button variant="outline" size="lg" className="flex items-center">
                    <Info className="h-5 w-5 mr-2" />
                    Contact Support
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};
